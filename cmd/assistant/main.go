// main.go - Koopa's Personal Development Assistant
// Cyberpunk-themed development environment with modular architecture
package main

import (
	"log"

	"assistant-go/internal/app"
	"assistant-go/internal/config"
)

func main() {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatal("Failed to load config:", err)
	}

	// Create the application (modules are automatically registered in app.New)
	application := app.New(cfg)

	// Run the application
	if err = application.Run(); err != nil {
		log.Fatal("Application error:", err)
	}
}
