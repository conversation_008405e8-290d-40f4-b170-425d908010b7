// Package cybertheme provides enhanced layout utilities for responsive design
// Implements Material Design 3 principles with cyberpunk aesthetic
package cybertheme

import (
	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"
)

// ResponsiveLayout provides responsive layout utilities for module views
type ResponsiveLayout struct {
	theme *CyberTheme
}

// NewResponsiveLayout creates a new responsive layout helper
func NewResponsiveLayout(theme *CyberTheme) *ResponsiveLayout {
	return &ResponsiveLayout{
		theme: theme,
	}
}

// CreateModuleContainer creates a responsive container for module content
// This ensures content fills the entire available space properly
func (rl *ResponsiveLayout) CreateModuleContainer(content fyne.CanvasObject) *container.Scroll {
	// Create main container with proper padding
	mainContainer := container.NewVBox(content)
	
	// Create scrollable container that expands to fill available space
	scrollContainer := container.NewScroll(mainContainer)
	
	// Set minimum size but allow expansion
	scrollContainer.SetMinSize(fyne.NewSize(800, 600))
	
	return scrollContainer
}

// CreateModuleHeader creates a standardized module header
func (rl *ResponsiveLayout) CreateModuleHeader(title, subtitle string) fyne.CanvasObject {
	// Create title with cyberpunk styling
	titleWidget := widget.NewRichTextFromMarkdown("# " + title)
	
	// Create subtitle if provided
	var subtitleWidget *widget.RichText
	if subtitle != "" {
		subtitleWidget = widget.NewRichTextFromMarkdown("**" + subtitle + "**")
	}
	
	// Create header container
	if subtitleWidget != nil {
		return container.NewVBox(
			titleWidget,
			subtitleWidget,
			widget.NewSeparator(),
		)
	}
	
	return container.NewVBox(
		titleWidget,
		widget.NewSeparator(),
	)
}

// CreateSectionCard creates a Material Design 3 card for content sections
func (rl *ResponsiveLayout) CreateSectionCard(title string, content fyne.CanvasObject) *CyberCard {
	return NewCyberCard(title, content, rl.theme, 1)
}

// CreateButtonRow creates a row of buttons with consistent spacing
func (rl *ResponsiveLayout) CreateButtonRow(buttons ...*widget.Button) fyne.CanvasObject {
	// Apply consistent sizing to all buttons
	for _, button := range buttons {
		button.Resize(fyne.NewSize(140, 40))
		button.Importance = widget.HighImportance
	}
	
	// Create horizontal container with proper spacing
	buttonObjects := make([]fyne.CanvasObject, len(buttons))
	for i, button := range buttons {
		buttonObjects[i] = button
	}
	
	return container.NewHBox(buttonObjects...)
}

// CreateTwoColumnLayout creates a responsive two-column layout
func (rl *ResponsiveLayout) CreateTwoColumnLayout(left, right fyne.CanvasObject) fyne.CanvasObject {
	return container.NewHBox(
		container.NewBorder(nil, nil, nil, nil, left),
		container.NewBorder(nil, nil, nil, nil, right),
	)
}

// CreateThreeColumnLayout creates a responsive three-column layout
func (rl *ResponsiveLayout) CreateThreeColumnLayout(left, center, right fyne.CanvasObject) fyne.CanvasObject {
	return container.NewHBox(
		container.NewBorder(nil, nil, nil, nil, left),
		container.NewBorder(nil, nil, nil, nil, center),
		container.NewBorder(nil, nil, nil, nil, right),
	)
}

// CreateStatusSection creates a standardized status section
func (rl *ResponsiveLayout) CreateStatusSection(statusText string) fyne.CanvasObject {
	statusLabel := widget.NewLabel(statusText)
	statusLabel.TextStyle.Bold = true
	
	return container.NewVBox(
		widget.NewSeparator(),
		statusLabel,
	)
}

// CreateListSection creates a standardized list section with header
func (rl *ResponsiveLayout) CreateListSection(title string, list *widget.List) fyne.CanvasObject {
	header := widget.NewLabel(title)
	header.TextStyle.Bold = true
	
	// Set optimal list size for better layout
	list.Resize(fyne.NewSize(0, 200)) // Height only, width will expand
	
	return container.NewVBox(
		header,
		list,
	)
}

// CreateInputSection creates a standardized input section
func (rl *ResponsiveLayout) CreateInputSection(title string, input fyne.CanvasObject) fyne.CanvasObject {
	header := widget.NewLabel(title)
	header.TextStyle.Bold = true
	
	return container.NewVBox(
		header,
		input,
	)
}

// CreateFullWidthContainer creates a container that expands to full width
func (rl *ResponsiveLayout) CreateFullWidthContainer(content fyne.CanvasObject) fyne.CanvasObject {
	return container.NewBorder(nil, nil, nil, nil, content)
}

// ApplyResponsiveSpacing applies consistent spacing following Material Design 3
func (rl *ResponsiveLayout) ApplyResponsiveSpacing(objects ...fyne.CanvasObject) fyne.CanvasObject {
	// Add separators between objects for consistent spacing
	spacedObjects := make([]fyne.CanvasObject, 0, len(objects)*2-1)
	
	for i, obj := range objects {
		spacedObjects = append(spacedObjects, obj)
		
		// Add separator between objects (but not after the last one)
		if i < len(objects)-1 {
			spacedObjects = append(spacedObjects, widget.NewSeparator())
		}
	}
	
	return container.NewVBox(spacedObjects...)
}

// CreateExpandingContainer creates a container that expands to fill available space
func (rl *ResponsiveLayout) CreateExpandingContainer(content fyne.CanvasObject) fyne.CanvasObject {
	// Use border layout to ensure content expands
	return container.NewBorder(nil, nil, nil, nil, content)
}

// CreateGridLayout creates a responsive grid layout
func (rl *ResponsiveLayout) CreateGridLayout(columns int, objects ...fyne.CanvasObject) fyne.CanvasObject {
	// Create grid container
	grid := container.NewGridWithColumns(columns, objects...)
	
	// Wrap in expanding container
	return rl.CreateExpandingContainer(grid)
}

// CreateTabContainer creates a tabbed container with cyberpunk styling
func (rl *ResponsiveLayout) CreateTabContainer() *container.AppTabs {
	tabs := container.NewAppTabs()
	
	// Apply cyberpunk styling
	tabs.SetTabLocation(container.TabLocationTop)
	
	return tabs
}

// CreateSplitContainer creates a split container with proper proportions
func (rl *ResponsiveLayout) CreateSplitContainer(leading, trailing fyne.CanvasObject, horizontal bool) *container.Split {
	var split *container.Split
	
	if horizontal {
		split = container.NewHSplit(leading, trailing)
		split.SetOffset(0.3) // 30% for leading, 70% for trailing
	} else {
		split = container.NewVSplit(leading, trailing)
		split.SetOffset(0.4) // 40% for leading, 60% for trailing
	}
	
	return split
}

// CreateToolbar creates a standardized toolbar
func (rl *ResponsiveLayout) CreateToolbar(buttons ...*widget.Button) fyne.CanvasObject {
	// Apply consistent styling to toolbar buttons
	for _, button := range buttons {
		button.Resize(fyne.NewSize(100, 36)) // Compact toolbar buttons
		button.Importance = widget.MediumImportance
	}
	
	buttonObjects := make([]fyne.CanvasObject, len(buttons))
	for i, button := range buttons {
		buttonObjects[i] = button
	}
	
	return container.NewHBox(buttonObjects...)
}

// CreateContentArea creates a main content area with proper padding and scrolling
func (rl *ResponsiveLayout) CreateContentArea(content fyne.CanvasObject) *container.Scroll {
	// Add padding around content
	paddedContent := container.NewPadded(content)
	
	// Create scrollable container
	scrollContainer := container.NewScroll(paddedContent)
	
	// Set minimum size but allow expansion
	scrollContainer.SetMinSize(fyne.NewSize(600, 400))
	
	return scrollContainer
}

// CreateSidePanel creates a side panel with consistent styling
func (rl *ResponsiveLayout) CreateSidePanel(content fyne.CanvasObject, width float32) fyne.CanvasObject {
	// Create container with fixed width
	panel := container.NewBorder(nil, nil, nil, nil, content)
	panel.Resize(fyne.NewSize(width, 0)) // Fixed width, flexible height
	
	return panel
}
