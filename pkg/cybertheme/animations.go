// Package cybertheme provides animation utilities for smooth transitions
// Implements Material Design 3 motion principles with cyberpunk aesthetic
package cybertheme

import (
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/animation"
	"fyne.io/fyne/v2/container"
)

// AnimationManager handles smooth transitions between UI states
type AnimationManager struct {
	theme *CyberTheme
}

// NewAnimationManager creates a new animation manager
func NewAnimationManager(theme *CyberTheme) *AnimationManager {
	return &AnimationManager{
		theme: theme,
	}
}

// ModuleTransition handles smooth transitions between modules
type ModuleTransition struct {
	manager     *AnimationManager
	container   *fyne.Container
	currentView fyne.CanvasObject
	newView     fyne.CanvasObject
	duration    time.Duration
}

// NewModuleTransition creates a new module transition
func (am *AnimationManager) NewModuleTransition(container *fyne.Container, duration time.Duration) *ModuleTransition {
	return &ModuleTransition{
		manager:   am,
		container: container,
		duration:  duration,
	}
}

// SwitchToModule performs a smooth transition to a new module
func (mt *ModuleTransition) SwitchToModule(newView fyne.CanvasObject, callback func()) {
	if mt.currentView == nil {
		// First time - no animation needed
		mt.container.Objects = []fyne.CanvasObject{newView}
		mt.currentView = newView
		mt.container.Refresh()
		if callback != nil {
			callback()
		}
		return
	}

	// Perform fade transition
	mt.fadeTransition(newView, callback)
}

// fadeTransition performs a fade out/in transition
func (mt *ModuleTransition) fadeTransition(newView fyne.CanvasObject, callback func()) {
	oldView := mt.currentView
	
	// Set initial alpha for new view
	newView.Move(fyne.NewPos(0, 0))
	newView.Resize(mt.container.Size())
	
	// Add new view to container (initially invisible)
	mt.container.Add(newView)
	
	// Fade out old view
	fadeOut := animation.NewAlpha(oldView, 0, mt.duration/2)
	fadeOut.Curve = animation.EaseInOut
	
	// Fade in new view after fade out completes
	fadeOut.OnComplete = func() {
		// Remove old view
		mt.container.Remove(oldView)
		
		// Fade in new view
		fadeIn := animation.NewAlpha(newView, 1, mt.duration/2)
		fadeIn.Curve = animation.EaseInOut
		fadeIn.OnComplete = func() {
			mt.currentView = newView
			if callback != nil {
				callback()
			}
		}
		fadeIn.Start()
	}
	
	fadeOut.Start()
}

// slideTransition performs a slide transition (left to right)
func (mt *ModuleTransition) slideTransition(newView fyne.CanvasObject, callback func()) {
	oldView := mt.currentView
	containerSize := mt.container.Size()
	
	// Position new view off-screen to the right
	newView.Move(fyne.NewPos(containerSize.Width, 0))
	newView.Resize(containerSize)
	mt.container.Add(newView)
	
	// Slide old view out to the left
	slideOutOld := animation.NewPosition(oldView, fyne.NewPos(-containerSize.Width, 0), mt.duration)
	slideOutOld.Curve = animation.EaseInOut
	
	// Slide new view in from the right
	slideInNew := animation.NewPosition(newView, fyne.NewPos(0, 0), mt.duration)
	slideInNew.Curve = animation.EaseInOut
	slideInNew.OnComplete = func() {
		mt.container.Remove(oldView)
		mt.currentView = newView
		if callback != nil {
			callback()
		}
	}
	
	// Start both animations simultaneously
	slideOutOld.Start()
	slideInNew.Start()
}

// ButtonHoverEffect creates a hover effect for buttons
type ButtonHoverEffect struct {
	button   fyne.CanvasObject
	theme    *CyberTheme
	original fyne.Size
	hovered  bool
}

// NewButtonHoverEffect creates a new button hover effect
func (am *AnimationManager) NewButtonHoverEffect(button fyne.CanvasObject) *ButtonHoverEffect {
	return &ButtonHoverEffect{
		button:   button,
		theme:    am.theme,
		original: button.Size(),
		hovered:  false,
	}
}

// OnMouseIn handles mouse enter events
func (bhe *ButtonHoverEffect) OnMouseIn() {
	if bhe.hovered {
		return
	}
	bhe.hovered = true
	
	// Scale up slightly
	newSize := fyne.NewSize(
		bhe.original.Width*1.05,
		bhe.original.Height*1.05,
	)
	
	scaleAnimation := animation.NewSize(bhe.button, newSize, time.Millisecond*150)
	scaleAnimation.Curve = animation.EaseOut
	scaleAnimation.Start()
}

// OnMouseOut handles mouse leave events
func (bhe *ButtonHoverEffect) OnMouseOut() {
	if !bhe.hovered {
		return
	}
	bhe.hovered = false
	
	// Scale back to original
	scaleAnimation := animation.NewSize(bhe.button, bhe.original, time.Millisecond*150)
	scaleAnimation.Curve = animation.EaseOut
	scaleAnimation.Start()
}

// PulseEffect creates a pulsing effect for status indicators
type PulseEffect struct {
	object   fyne.CanvasObject
	theme    *CyberTheme
	running  bool
	original float32
}

// NewPulseEffect creates a new pulse effect
func (am *AnimationManager) NewPulseEffect(object fyne.CanvasObject) *PulseEffect {
	return &PulseEffect{
		object:   object,
		theme:    am.theme,
		running:  false,
		original: 1.0,
	}
}

// Start begins the pulsing animation
func (pe *PulseEffect) Start() {
	if pe.running {
		return
	}
	pe.running = true
	pe.pulse()
}

// Stop ends the pulsing animation
func (pe *PulseEffect) Stop() {
	pe.running = false
}

// pulse performs one pulse cycle
func (pe *PulseEffect) pulse() {
	if !pe.running {
		return
	}
	
	// Pulse out
	pulseOut := animation.NewAlpha(pe.object, 0.3, time.Millisecond*800)
	pulseOut.Curve = animation.EaseInOut
	pulseOut.OnComplete = func() {
		if !pe.running {
			return
		}
		
		// Pulse in
		pulseIn := animation.NewAlpha(pe.object, 1.0, time.Millisecond*800)
		pulseIn.Curve = animation.EaseInOut
		pulseIn.OnComplete = func() {
			// Continue pulsing
			time.AfterFunc(time.Millisecond*200, pe.pulse)
		}
		pulseIn.Start()
	}
	pulseOut.Start()
}

// GlowEffect creates a glow effect for cyberpunk elements
type GlowEffect struct {
	object  fyne.CanvasObject
	theme   *CyberTheme
	running bool
}

// NewGlowEffect creates a new glow effect
func (am *AnimationManager) NewGlowEffect(object fyne.CanvasObject) *GlowEffect {
	return &GlowEffect{
		object:  object,
		theme:   am.theme,
		running: false,
	}
}

// Start begins the glow animation
func (ge *GlowEffect) Start() {
	if ge.running {
		return
	}
	ge.running = true
	ge.glow()
}

// Stop ends the glow animation
func (ge *GlowEffect) Stop() {
	ge.running = false
}

// glow performs one glow cycle
func (ge *GlowEffect) glow() {
	if !ge.running {
		return
	}
	
	// Glow up
	glowUp := animation.NewAlpha(ge.object, 1.0, time.Millisecond*1000)
	glowUp.Curve = animation.EaseInOut
	glowUp.OnComplete = func() {
		if !ge.running {
			return
		}
		
		// Glow down
		glowDown := animation.NewAlpha(ge.object, 0.6, time.Millisecond*1000)
		glowDown.Curve = animation.EaseInOut
		glowDown.OnComplete = func() {
			// Continue glowing
			time.AfterFunc(time.Millisecond*100, ge.glow)
		}
		glowDown.Start()
	}
	glowUp.Start()
}

// LoadingSpinner creates a cyberpunk-styled loading animation
type LoadingSpinner struct {
	container *container.Rotate
	theme     *CyberTheme
	running   bool
}

// NewLoadingSpinner creates a new loading spinner
func (am *AnimationManager) NewLoadingSpinner() *LoadingSpinner {
	// Create a simple rotating element for now
	// TODO: Create custom cyberpunk spinner graphic
	content := container.NewWithoutLayout()
	rotateContainer := container.NewRotate(0, content)
	
	return &LoadingSpinner{
		container: rotateContainer,
		theme:     am.theme,
		running:   false,
	}
}

// Start begins the spinning animation
func (ls *LoadingSpinner) Start() {
	if ls.running {
		return
	}
	ls.running = true
	ls.spin()
}

// Stop ends the spinning animation
func (ls *LoadingSpinner) Stop() {
	ls.running = false
}

// GetContainer returns the spinner container
func (ls *LoadingSpinner) GetContainer() *container.Rotate {
	return ls.container
}

// spin performs continuous rotation
func (ls *LoadingSpinner) spin() {
	if !ls.running {
		return
	}
	
	// Rotate 360 degrees
	rotation := animation.NewFloat(0, 360, time.Second*2)
	rotation.Curve = animation.EaseInOut
	rotation.OnUpdate = func(angle float64) {
		ls.container.SetAngle(float32(angle))
	}
	rotation.OnComplete = func() {
		// Continue spinning
		if ls.running {
			ls.spin()
		}
	}
	rotation.Start()
}
