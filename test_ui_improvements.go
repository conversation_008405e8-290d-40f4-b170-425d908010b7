// Test file to verify UI improvements and threading fixes
// This file demonstrates the enhanced GUI with fixed threading issues
package main

import (
	"fmt"
	"log"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/app"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"

	"assistant-go/internal/config"
	"assistant-go/internal/gui"
	"assistant-go/pkg/cybertheme"
)

func main() {
	// Create test configuration
	cfg := &config.Config{
		App: config.AppConfig{
			Name:        "Assistant-Go Test",
			Version:     "1.0.0-test",
			Environment: "testing",
		},
		Theme: config.ThemeConfig{
			PrimaryColor:   "#0096FF",
			SecondaryColor: "#00FFFF",
			Background:     "#000000",
			FontSize:       13.0,
		},
	}

	// Create Fyne application
	fyneApp := app.NewWithID("com.koopa.assistant-go-test")

	// Create cyberpunk theme
	theme := cybertheme.NewCustomCyberTheme(
		cfg.Theme.PrimaryColor,
		cfg.Theme.SecondaryColor,
		cfg.Theme.Background,
	)
	theme.ApplyToApp(fyneApp)

	// Create test modules map
	modules := make(map[string]interface{})

	// Create GUI application
	guiApp, err := gui.New(fyneApp, cfg, modules)
	if err != nil {
		log.Fatalf("Failed to create GUI application: %v", err)
	}

	// Create test window to demonstrate improvements
	testWindow := fyneApp.NewWindow("UI Improvements Test")
	testWindow.Resize(fyne.NewSize(1200, 800))

	// Create test content to demonstrate scrolling and threading fixes
	testContent := container.NewVBox(
		widget.NewLabel("🖥️ UI IMPROVEMENTS TEST"),
		widget.NewSeparator(),
		widget.NewLabel("Threading Issues Fixed:"),
		widget.NewLabel("✅ StatusBar time updates now use fyne.Do()"),
		widget.NewLabel("✅ Temporary messages use fyne.Do()"),
		widget.NewLabel("✅ Progress animations use fyne.Do()"),
		widget.NewLabel("✅ Task execution UI updates use fyne.Do()"),
		widget.NewLabel("✅ Dialog operations use fyne.Do()"),
		widget.NewSeparator(),
		widget.NewLabel("UI/UX Improvements:"),
		widget.NewLabel("✅ Reduced sidebar button spacing"),
		widget.NewLabel("✅ Improved button text contrast (WCAG AA compliant)"),
		widget.NewLabel("✅ Enhanced Material Design 3 spacing"),
		widget.NewLabel("✅ Fixed text cutoff issues"),
		widget.NewLabel("✅ Added proper scrolling support"),
		widget.NewLabel("✅ Responsive design improvements"),
		widget.NewSeparator(),
		widget.NewLabel("Cyberpunk Theme Enhancements:"),
		widget.NewLabel("✅ Better contrast ratios for text visibility"),
		widget.NewLabel("✅ Enhanced button styling with CyberButton"),
		widget.NewLabel("✅ Material Design 3 typography scale"),
		widget.NewLabel("✅ Improved color system"),
		widget.NewSeparator(),
	)

	// Add many more items to test scrolling
	for i := 1; i <= 20; i++ {
		testContent.Add(widget.NewLabel(fmt.Sprintf("Test scrolling item %d", i)))
	}

	// Create scrollable container
	scrollContainer := container.NewScroll(testContent)
	scrollContainer.Direction = container.ScrollBoth

	// Test threading with a timer
	go func() {
		ticker := time.NewTicker(2 * time.Second)
		defer ticker.Stop()

		counter := 0
		for range ticker.C {
			counter++
			// This should NOT cause threading errors anymore
			fyne.Do(func() {
				testContent.Add(widget.NewLabel(fmt.Sprintf("Dynamic item %d (added safely)", counter)))
				scrollContainer.Refresh()
			})

			if counter >= 5 {
				break
			}
		}
	}()

	testWindow.SetContent(scrollContainer)
	testWindow.Show()

	// Show the main GUI application
	guiApp.ShowAndRun()
}
