// Package cloudflare provides Cloudflare API management functionality
// manager.go - Main Cloudflare module manager
package cloudflare

import (
	"context"
	"fmt"
	"log/slog"
	"sync"
	"time"

	"assistant-go/internal/config"
	"assistant-go/internal/types"
)

// Manager handles Cloudflare operations using domain-specific managers
type Manager struct {
	config *config.Config
	logger *slog.Logger

	// Domain-specific managers
	dnsManager    *DNSManager
	zoneManager   *ZoneManager
	cdnManager    *CDNManager
	secManager    *SecurityManager
	tunnelManager *TunnelManager

	// Module state
	ctx     context.Context
	cancel  context.CancelFunc
	running bool
	mu      sync.RWMutex

	// API configuration
	apiToken string
	apiEmail string
	apiKey   string
}

// NewManager creates a new Cloudflare manager with domain-specific structure
func NewManager() *Manager {
	ctx, cancel := context.WithCancel(context.Background())

	// Note: DNS manager will be initialized in Initialize() method with proper config
	zoneManager := NewZoneManager()
	cdnManager := NewCDNManager()
	secManager := NewSecurityManager()
	tunnelManager := NewTunnelManager()

	return &Manager{
		zoneManager:   zoneManager,
		cdnManager:    cdnManager,
		secManager:    secManager,
		tunnelManager: tunnelManager,
		ctx:           ctx,
		cancel:        cancel,
		logger:        slog.Default().With("component", "cloudflare"),
	}
}

// Initialize implements the Module interface
func (m *Manager) Initialize(ctx context.Context, cfg *config.Config) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.config = cfg
	m.logger.Info("Initializing Cloudflare module")

	// Get API credentials from config
	if cfg.Cloudflare.APIToken != "" {
		m.apiToken = cfg.Cloudflare.APIToken
	} else if cfg.Cloudflare.APIEmail != "" && cfg.Cloudflare.APIKey != "" {
		m.apiEmail = cfg.Cloudflare.APIEmail
		m.apiKey = cfg.Cloudflare.APIKey
	} else {
		m.logger.Warn("Cloudflare API credentials not configured - module will run in limited mode")
		// Don't fail initialization - allow module to run in limited mode
	}

	// Initialize DNS manager with proper config
	dnsManager, err := NewDNSManager(&cfg.Cloudflare)
	if err != nil {
		return fmt.Errorf("failed to create DNS manager: %w", err)
	}
	m.dnsManager = dnsManager

	if err := m.zoneManager.Initialize(ctx, cfg, m.getAPIConfig()); err != nil {
		return fmt.Errorf("failed to initialize zone manager: %w", err)
	}

	if err := m.cdnManager.Initialize(ctx, cfg, m.getAPIConfig()); err != nil {
		return fmt.Errorf("failed to initialize CDN manager: %w", err)
	}

	if err := m.secManager.Initialize(ctx, cfg, m.getAPIConfig()); err != nil {
		return fmt.Errorf("failed to initialize security manager: %w", err)
	}

	if err := m.tunnelManager.Initialize(ctx, cfg, m.getAPIConfig()); err != nil {
		return fmt.Errorf("failed to initialize tunnel manager: %w", err)
	}

	m.logger.Info("Cloudflare module initialized successfully")
	return nil
}

// Start implements the Module interface
func (m *Manager) Start(ctx context.Context) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.running {
		return fmt.Errorf("Cloudflare module is already running")
	}

	m.logger.Info("Starting Cloudflare module")

	// Start all sub-managers
	if err := m.dnsManager.Start(); err != nil {
		return fmt.Errorf("failed to start DNS manager: %w", err)
	}

	if err := m.zoneManager.Start(); err != nil {
		return fmt.Errorf("failed to start zone manager: %w", err)
	}

	if err := m.cdnManager.Start(); err != nil {
		return fmt.Errorf("failed to start CDN manager: %w", err)
	}

	if err := m.secManager.Start(); err != nil {
		return fmt.Errorf("failed to start security manager: %w", err)
	}

	if err := m.tunnelManager.Start(); err != nil {
		return fmt.Errorf("failed to start tunnel manager: %w", err)
	}

	m.running = true
	m.logger.Info("Cloudflare module started successfully")
	return nil
}

// Stop implements the Module interface
func (m *Manager) Stop(ctx context.Context) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if !m.running {
		return nil
	}

	m.logger.Info("Stopping Cloudflare module")

	// Stop all sub-managers
	m.dnsManager.Stop()
	m.zoneManager.Stop()
	m.cdnManager.Stop()
	m.secManager.Stop()

	m.cancel()
	m.running = false

	m.logger.Info("Cloudflare module stopped")
	return nil
}

// Health implements the Module interface
func (m *Manager) Health() types.ModuleHealth {
	m.mu.RLock()
	defer m.mu.RUnlock()

	health := types.ModuleHealth{
		Status:    "healthy",
		Message:   "Cloudflare API connected",
		LastCheck: time.Now(),
	}

	if !m.running {
		health.Status = "unhealthy"
		health.Message = "Module not running"
		return health
	}

	// Check API connectivity
	if !m.isAPIAvailable() {
		health.Status = "unhealthy"
		health.Message = "Cloudflare API not available"
		return health
	}

	// Get summary information
	zoneCount := m.zoneManager.GetZoneCount()
	recordCount := m.dnsManager.GetRecordCount()
	tunnelCount := m.tunnelManager.GetTunnelCount()

	health.Message = fmt.Sprintf("%d zones, %d DNS records, %d tunnels managed", zoneCount, recordCount, tunnelCount)

	return health
}

// Name implements the Module interface
func (m *Manager) Name() string {
	return "Cloudflare"
}

// getAPIConfig returns API configuration for sub-managers
func (m *Manager) getAPIConfig() APIConfig {
	return APIConfig{
		Token: m.apiToken,
		Email: m.apiEmail,
		Key:   m.apiKey,
	}
}

// isAPIAvailable checks if Cloudflare API is available
func (m *Manager) isAPIAvailable() bool {
	// TODO: Implement actual API connectivity check
	// This would make a simple API call to verify credentials
	return true
}

// GetDNSManager returns the DNS manager
func (m *Manager) GetDNSManager() *DNSManager {
	return m.dnsManager
}

// GetZoneManager returns the zone manager
func (m *Manager) GetZoneManager() *ZoneManager {
	return m.zoneManager
}

// GetCDNManager returns the CDN manager
func (m *Manager) GetCDNManager() *CDNManager {
	return m.cdnManager
}

// GetSecurityManager returns the security manager
func (m *Manager) GetSecurityManager() *SecurityManager {
	return m.secManager
}

// GetTunnelManager returns the tunnel manager
func (m *Manager) GetTunnelManager() *TunnelManager {
	return m.tunnelManager
}

// APIConfig holds Cloudflare API configuration
type APIConfig struct {
	Token string
	Email string
	Key   string
}

// Zone operations
func (m *Manager) ListZones() ([]ZoneInfo, error) {
	return m.zoneManager.ListZones()
}

func (m *Manager) GetZone(zoneID string) (*ZoneInfo, error) {
	return m.zoneManager.GetZone(zoneID)
}

func (m *Manager) CreateZone(name string) (*ZoneInfo, error) {
	return m.zoneManager.CreateZone(name)
}

func (m *Manager) DeleteZone(zoneID string) error {
	return m.zoneManager.DeleteZone(zoneID)
}

// DNS operations
func (m *Manager) ListDNSRecords(zoneID string) ([]DNSRecord, error) {
	return m.dnsManager.ListRecords(zoneID)
}

func (m *Manager) CreateDNSRecord(zoneID string, record DNSRecord) (*DNSRecord, error) {
	return m.dnsManager.CreateRecord(zoneID, record)
}

func (m *Manager) UpdateDNSRecord(zoneID, recordID string, record DNSRecord) (*DNSRecord, error) {
	return m.dnsManager.UpdateRecord(zoneID, recordID, record)
}

func (m *Manager) DeleteDNSRecord(zoneID, recordID string) error {
	return m.dnsManager.DeleteRecord(zoneID, recordID)
}

// CDN operations
func (m *Manager) PurgeCache(zoneID string, files []string) error {
	return m.cdnManager.PurgeCache(zoneID, files)
}

// Tunnel operations
func (m *Manager) ListTunnels() ([]TunnelInfo, error) {
	return m.tunnelManager.ListTunnels()
}

func (m *Manager) GetTunnel(tunnelID string) (*TunnelInfo, error) {
	return m.tunnelManager.GetTunnel(tunnelID)
}

func (m *Manager) CreateTunnel(accountID, name, secret string) (*TunnelInfo, error) {
	return m.tunnelManager.CreateTunnel(m.ctx, accountID, name, secret)
}

func (m *Manager) DeleteTunnel(accountID, tunnelID string) error {
	return m.tunnelManager.DeleteTunnel(m.ctx, accountID, tunnelID)
}

func (m *Manager) UpdateTunnelConfig(accountID, tunnelID string, config TunnelConfiguration) error {
	return m.tunnelManager.UpdateTunnelConfig(m.ctx, accountID, tunnelID, config)
}

func (m *Manager) GetTunnelConnections(accountID, tunnelID string) ([]TunnelConnection, error) {
	return m.tunnelManager.GetTunnelConnections(m.ctx, accountID, tunnelID)
}

func (m *Manager) GetTunnelToken(accountID, tunnelID string) (string, error) {
	return m.tunnelManager.GetTunnelToken(m.ctx, accountID, tunnelID)
}

func (m *Manager) GetCacheSettings(zoneID string) (*CacheSettings, error) {
	return m.cdnManager.GetCacheSettings(zoneID)
}

func (m *Manager) UpdateCacheSettings(zoneID string, settings CacheSettings) error {
	return m.cdnManager.UpdateCacheSettings(zoneID, settings)
}

// Security operations
func (m *Manager) GetSecurityLevel(zoneID string) (string, error) {
	return m.secManager.GetSecurityLevel(zoneID)
}

func (m *Manager) SetSecurityLevel(zoneID, level string) error {
	return m.secManager.SetSecurityLevel(zoneID, level)
}

func (m *Manager) GetFirewallRules(zoneID string) ([]FirewallRule, error) {
	return m.secManager.GetFirewallRules(zoneID)
}

func (m *Manager) CreateFirewallRule(zoneID string, rule FirewallRule) (*FirewallRule, error) {
	return m.secManager.CreateFirewallRule(zoneID, rule)
}

// Analytics operations
func (m *Manager) GetAnalytics(zoneID string, since, until time.Time) (*Analytics, error) {
	// TODO: Implement analytics manager
	return &Analytics{}, nil
}

// Page Rules operations
func (m *Manager) GetPageRules(zoneID string) ([]PageRule, error) {
	// TODO: Implement page rules manager
	return []PageRule{}, nil
}

// TODO: Implement Workers management
// TODO: Implement SSL/TLS certificate management
// TODO: Implement Load Balancer management
// TODO: Implement Rate Limiting rules
// TODO: Implement Access policies
// TODO: Implement Stream video management
// TODO: Implement Images optimization
// TODO: Implement Spectrum applications
