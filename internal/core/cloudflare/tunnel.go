// Package cloudflare provides Cloudflare Tunnel management functionality
// tunnel.go - Handles Cloudflare Tunnel operations using official SDK v4
package cloudflare

import (
	"context"
	"fmt"
	"log/slog"
	"sync"
	"time"

	"assistant-go/internal/config"

	"github.com/cloudflare/cloudflare-go/v4"
	"github.com/cloudflare/cloudflare-go/v4/option"
)

// TunnelManager handles Cloudflare Tunnel operations
type TunnelManager struct {
	logger    *slog.Logger
	apiConfig APIConfig
	client    *cloudflare.Client
	mu        sync.RWMutex

	// Tunnel state
	tunnels map[string]*TunnelInfo
}

// TunnelInfo represents a Cloudflare Tunnel
type TunnelInfo struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	AccountID   string                 `json:"account_id"`
	Secret      string                 `json:"secret,omitempty"`
	CreatedAt   time.Time              `json:"created_at"`
	DeletedAt   *time.Time             `json:"deleted_at,omitempty"`
	Connections []TunnelConnection     `json:"connections"`
	Status      string                 `json:"status"`
	Config      *TunnelConfiguration   `json:"config,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// TunnelConnection represents an active tunnel connection
type TunnelConnection struct {
	ID            string    `json:"id"`
	Features      []string  `json:"features"`
	Version       string    `json:"version"`
	Arch          string    `json:"arch"`
	ConnectedAt   time.Time `json:"connected_at"`
	IsActive      bool      `json:"is_active"`
	OriginIP      string    `json:"origin_ip"`
	EdgeLocation  string    `json:"edge_location"`
	LastHeartbeat time.Time `json:"last_heartbeat"`
}

// TunnelConfiguration represents tunnel configuration
type TunnelConfiguration struct {
	Ingress       []IngressRule        `json:"ingress"`
	WarpRouting   *WarpRoutingConfig   `json:"warp-routing,omitempty"`
	OriginRequest *OriginRequestConfig `json:"originRequest,omitempty"`
}

// IngressRule represents a tunnel ingress rule
type IngressRule struct {
	Hostname string                 `json:"hostname,omitempty"`
	Path     string                 `json:"path,omitempty"`
	Service  string                 `json:"service"`
	Options  map[string]interface{} `json:"options,omitempty"`
}

// WarpRoutingConfig represents WARP routing configuration
type WarpRoutingConfig struct {
	Enabled bool `json:"enabled"`
}

// OriginRequestConfig represents origin request configuration
type OriginRequestConfig struct {
	ConnectTimeout         *time.Duration `json:"connectTimeout,omitempty"`
	TLSTimeout             *time.Duration `json:"tlsTimeout,omitempty"`
	TCPKeepAlive           *time.Duration `json:"tcpKeepAlive,omitempty"`
	NoHappyEyeballs        *bool          `json:"noHappyEyeballs,omitempty"`
	KeepAliveTimeout       *time.Duration `json:"keepAliveTimeout,omitempty"`
	HTTPHostHeader         string         `json:"httpHostHeader,omitempty"`
	OriginServerName       string         `json:"originServerName,omitempty"`
	CAPool                 string         `json:"caPool,omitempty"`
	NoTLSVerify            *bool          `json:"noTLSVerify,omitempty"`
	DisableChunkedEncoding *bool          `json:"disableChunkedEncoding,omitempty"`
}

// NewTunnelManager creates a new tunnel manager
func NewTunnelManager() *TunnelManager {
	return &TunnelManager{
		logger:  slog.Default().With("component", "cloudflare-tunnels"),
		tunnels: make(map[string]*TunnelInfo),
	}
}

// Initialize initializes the tunnel manager
func (tm *TunnelManager) Initialize(ctx context.Context, cfg *config.Config, apiConfig APIConfig) error {
	tm.logger.Info("Initializing Cloudflare Tunnel manager")

	tm.apiConfig = apiConfig

	// Initialize Cloudflare client if credentials are available
	if apiConfig.Token != "" {
		tm.client = cloudflare.NewClient(option.WithAPIToken(apiConfig.Token))
	} else if apiConfig.Email != "" && apiConfig.Key != "" {
		tm.client = cloudflare.NewClient(
			option.WithAPIKey(apiConfig.Key),
			option.WithAPIEmail(apiConfig.Email),
		)
	} else {
		tm.logger.Warn("No Cloudflare API credentials provided - tunnel manager will run in limited mode")
		// Don't fail initialization - allow tunnel manager to run in limited mode
	}

	// Load existing tunnels if client is available
	if tm.client != nil {
		if err := tm.loadTunnels(ctx); err != nil {
			tm.logger.Error("Failed to load tunnels", "error", err)
			// Continue initialization - tunnels can be loaded later
		}
	}

	tm.logger.Info("Cloudflare Tunnel manager initialized")
	return nil
}

// Start starts the tunnel manager
func (tm *TunnelManager) Start() error {
	tm.logger.Info("Starting Cloudflare Tunnel manager")
	// TODO: Start tunnel monitoring and health checks
	return nil
}

// Stop stops the tunnel manager
func (tm *TunnelManager) Stop() error {
	tm.logger.Info("Stopping Cloudflare Tunnel manager")
	// TODO: Cleanup resources
	return nil
}

// loadTunnels loads existing tunnels from Cloudflare
func (tm *TunnelManager) loadTunnels(ctx context.Context) error {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	// TODO: Implement tunnel loading using Cloudflare SDK
	// This would list all tunnels for the account
	tm.logger.Info("Loading tunnels from Cloudflare API")

	// For now, return success - actual implementation would use:
	// tunnels, err := tm.client.ListTunnels(ctx, cloudflare.AccountIdentifier(accountID), cloudflare.TunnelListParams{})

	return nil
}

// ListTunnels returns all tunnels
func (tm *TunnelManager) ListTunnels() ([]TunnelInfo, error) {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	tunnels := make([]TunnelInfo, 0, len(tm.tunnels))
	for _, tunnel := range tm.tunnels {
		tunnels = append(tunnels, *tunnel)
	}

	return tunnels, nil
}

// GetTunnel returns a specific tunnel by ID
func (tm *TunnelManager) GetTunnel(tunnelID string) (*TunnelInfo, error) {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	tunnel, exists := tm.tunnels[tunnelID]
	if !exists {
		return nil, fmt.Errorf("tunnel %s not found", tunnelID)
	}

	return tunnel, nil
}

// CreateTunnel creates a new Cloudflare Tunnel
func (tm *TunnelManager) CreateTunnel(ctx context.Context, accountID, name, secret string) (*TunnelInfo, error) {
	tm.logger.Info("Creating tunnel", "name", name, "account", accountID)

	if tm.client == nil {
		return nil, fmt.Errorf("Cloudflare API client not available - please configure API credentials")
	}

	// TODO: Implement tunnel creation using Cloudflare SDK
	// tunnel, err := tm.client.CreateTunnel(ctx, cloudflare.AccountIdentifier(accountID), cloudflare.TunnelCreateParams{
	//     Name:   name,
	//     Secret: secret,
	// })

	// For now, create a mock tunnel
	tunnel := &TunnelInfo{
		ID:          fmt.Sprintf("tunnel-%d", time.Now().Unix()),
		Name:        name,
		AccountID:   accountID,
		Secret:      secret,
		CreatedAt:   time.Now(),
		Status:      "inactive",
		Connections: []TunnelConnection{},
	}

	tm.mu.Lock()
	tm.tunnels[tunnel.ID] = tunnel
	tm.mu.Unlock()

	tm.logger.Info("Tunnel created", "id", tunnel.ID, "name", name)
	return tunnel, nil
}

// DeleteTunnel deletes a Cloudflare Tunnel
func (tm *TunnelManager) DeleteTunnel(ctx context.Context, accountID, tunnelID string) error {
	tm.logger.Info("Deleting tunnel", "id", tunnelID, "account", accountID)

	// TODO: Implement tunnel deletion using Cloudflare SDK
	// err := tm.client.DeleteTunnel(ctx, cloudflare.AccountIdentifier(accountID), tunnelID)

	tm.mu.Lock()
	delete(tm.tunnels, tunnelID)
	tm.mu.Unlock()

	tm.logger.Info("Tunnel deleted", "id", tunnelID)
	return nil
}

// UpdateTunnelConfig updates tunnel configuration
func (tm *TunnelManager) UpdateTunnelConfig(ctx context.Context, accountID, tunnelID string, config TunnelConfiguration) error {
	tm.logger.Info("Updating tunnel configuration", "id", tunnelID)

	// TODO: Implement configuration update using Cloudflare SDK
	// err := tm.client.UpdateTunnelConfiguration(ctx, cloudflare.AccountIdentifier(accountID), tunnelID, config)

	tm.mu.Lock()
	if tunnel, exists := tm.tunnels[tunnelID]; exists {
		tunnel.Config = &config
	}
	tm.mu.Unlock()

	tm.logger.Info("Tunnel configuration updated", "id", tunnelID)
	return nil
}

// GetTunnelConnections returns active connections for a tunnel
func (tm *TunnelManager) GetTunnelConnections(ctx context.Context, accountID, tunnelID string) ([]TunnelConnection, error) {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	tunnel, exists := tm.tunnels[tunnelID]
	if !exists {
		return nil, fmt.Errorf("tunnel %s not found", tunnelID)
	}

	// TODO: Implement connection retrieval using Cloudflare SDK
	// connections, err := tm.client.ListTunnelConnections(ctx, cloudflare.AccountIdentifier(accountID), tunnelID)

	return tunnel.Connections, nil
}

// GetTunnelToken generates a tunnel token for cloudflared
func (tm *TunnelManager) GetTunnelToken(ctx context.Context, accountID, tunnelID string) (string, error) {
	tm.logger.Info("Generating tunnel token", "id", tunnelID)

	// TODO: Implement token generation using Cloudflare SDK
	// token, err := tm.client.GetTunnelToken(ctx, cloudflare.AccountIdentifier(accountID), tunnelID)

	// For now, return a mock token
	token := fmt.Sprintf("tunnel-token-%s-%d", tunnelID, time.Now().Unix())

	tm.logger.Info("Tunnel token generated", "id", tunnelID)
	return token, nil
}

// GetTunnelCount returns the number of tunnels
func (tm *TunnelManager) GetTunnelCount() int {
	tm.mu.RLock()
	defer tm.mu.RUnlock()
	return len(tm.tunnels)
}
