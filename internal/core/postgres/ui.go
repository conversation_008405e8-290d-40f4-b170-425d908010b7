// Package postgres provides the UI components for PostgreSQL management
// Implements cyberpunk-styled database interface with scrolling support
package postgres

import (
	"fmt"
	"strconv"
	"strings"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/widget"

	"assistant-go/internal/config"
	"assistant-go/pkg/cybertheme"
)

// UI provides the user interface for PostgreSQL management
type UI struct {
	manager *Manager
	config  *config.Config
	theme   *cybertheme.CyberTheme

	// Main components
	content         *container.Scroll
	connectionList  *widget.List
	queryEditor     *widget.Entry
	resultTable     *widget.Table
	statusLabel     *widget.Label

	// Current state
	selectedConnection string
	queryHistory       []string
}

// NewUI creates a new PostgreSQL UI
func NewUI(manager *Manager, cfg *config.Config, theme *cybertheme.CyberTheme) *UI {
	ui := &UI{
		manager:      manager,
		config:       cfg,
		theme:        theme,
		queryHistory: make([]string, 0),
	}

	ui.createComponents()
	return ui
}

// createComponents creates all UI components
func (ui *UI) createComponents() {
	// Create connection list
	ui.createConnectionList()

	// Create query editor
	ui.createQueryEditor()

	// Create result table
	ui.createResultTable()

	// Create status label
	ui.statusLabel = widget.NewLabel(">>> DATABASE MODULE READY <<<")

	// Create main layout
	ui.createMainLayout()
}

// createConnectionList creates the connection management interface
func (ui *UI) createConnectionList() {
	connections := ui.manager.ListConnections()
	connectionNames := make([]string, 0, len(connections))

	for name := range connections {
		connectionNames = append(connectionNames, name)
	}

	ui.connectionList = widget.NewList(
		func() int {
			return len(connectionNames)
		},
		func() fyne.CanvasObject {
			return container.NewHBox(
				widget.NewIcon(nil), // TODO: Add connection status icon
				widget.NewLabel("Connection Name"),
				widget.NewLabel("Status"),
			)
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			if id < len(connectionNames) {
				name := connectionNames[id]
				conn, _ := ui.manager.GetConnection(name)

				cont := obj.(*fyne.Container)
				nameLabel := cont.Objects[1].(*widget.Label)
				statusLabel := cont.Objects[2].(*widget.Label)

				nameLabel.SetText(name)

				if conn != nil {
					status := conn.GetStatus()
					if status.Connected {
						statusLabel.SetText("🟢 Connected")
					} else {
						statusLabel.SetText("🔴 Disconnected")
					}
				}
			}
		},
	)

	ui.connectionList.OnSelected = func(id widget.ListItemID) {
		if id < len(connectionNames) {
			ui.selectedConnection = connectionNames[id]
			ui.updateStatus(fmt.Sprintf("Selected connection: %s", ui.selectedConnection))
		}
	}
}

// createQueryEditor creates the SQL query editor
func (ui *UI) createQueryEditor() {
	ui.queryEditor = widget.NewMultiLineEntry()
	ui.queryEditor.SetPlaceHolder("Enter your SQL query here...")
	ui.queryEditor.Resize(fyne.NewSize(600, 200))
	ui.queryEditor.SetText("SELECT version();") // Default query
}

// createResultTable creates the query result display table
func (ui *UI) createResultTable() {
	ui.resultTable = widget.NewTable(
		func() (int, int) { return 0, 0 }, // Will be updated with actual data
		func() fyne.CanvasObject {
			return widget.NewLabel("No data")
		},
		func(id widget.TableCellID, obj fyne.CanvasObject) {
			label := obj.(*widget.Label)
			label.SetText("No data")
		},
	)
}

// createMainLayout creates the main UI layout
func (ui *UI) createMainLayout() {
	// Create connection management section
	connectionSection := container.NewVBox(
		widget.NewLabel("🗄️ Database Connections"),
		widget.NewSeparator(),
		ui.connectionList,
		container.NewHBox(
			widget.NewButton("Add Connection", ui.showAddConnectionDialog),
			widget.NewButton("Test Connection", ui.testSelectedConnection),
			widget.NewButton("Refresh", ui.refreshConnections),
		),
	)

	// Create query section
	querySection := container.NewVBox(
		widget.NewLabel("📝 SQL Query Editor"),
		widget.NewSeparator(),
		ui.queryEditor,
		container.NewHBox(
			widget.NewButton("Execute Query", ui.executeQuery),
			widget.NewButton("Clear", func() { ui.queryEditor.SetText("") }),
			widget.NewButton("History", ui.showQueryHistory),
		),
	)

	// Create results section
	resultsSection := container.NewVBox(
		widget.NewLabel("📊 Query Results"),
		widget.NewSeparator(),
		container.NewScroll(ui.resultTable),
	)

	// Create main container with scrolling support
	mainContainer := container.NewVBox(
		widget.NewRichTextFromMarkdown(`
# 🗄️ PostgreSQL Navigator

**INTELLIGENT DATABASE MANAGEMENT**

> Advanced PostgreSQL management with AI-powered query optimization,
> visual explain plans, and intelligent error explanations.

---
`),
		connectionSection,
		widget.NewSeparator(),
		querySection,
		widget.NewSeparator(),
		resultsSection,
		widget.NewSeparator(),
		ui.statusLabel,
	)

	// Wrap in scroll container to handle overflow
	ui.content = container.NewScroll(mainContainer)
	ui.content.SetMinSize(fyne.NewSize(800, 600))
}

// showAddConnectionDialog shows the add connection dialog
func (ui *UI) showAddConnectionDialog() {
	// Create form fields
	nameEntry := widget.NewEntry()
	nameEntry.SetPlaceHolder("Connection name")

	hostEntry := widget.NewEntry()
	hostEntry.SetPlaceHolder("localhost")
	hostEntry.SetText("localhost")

	portEntry := widget.NewEntry()
	portEntry.SetPlaceHolder("5432")
	portEntry.SetText("5432")

	databaseEntry := widget.NewEntry()
	databaseEntry.SetPlaceHolder("postgres")
	databaseEntry.SetText("postgres")

	usernameEntry := widget.NewEntry()
	usernameEntry.SetPlaceHolder("postgres")
	usernameEntry.SetText("postgres")

	passwordEntry := widget.NewPasswordEntry()
	passwordEntry.SetPlaceHolder("Password")

	sslModeSelect := widget.NewSelect([]string{"disable", "require", "prefer"}, nil)
	sslModeSelect.SetSelected("prefer")

	// Create form
	form := container.NewVBox(
		widget.NewLabel("Add Database Connection"),
		widget.NewSeparator(),
		widget.NewForm(
			widget.NewFormItem("Name", nameEntry),
			widget.NewFormItem("Host", hostEntry),
			widget.NewFormItem("Port", portEntry),
			widget.NewFormItem("Database", databaseEntry),
			widget.NewFormItem("Username", usernameEntry),
			widget.NewFormItem("Password", passwordEntry),
			widget.NewFormItem("SSL Mode", sslModeSelect),
		),
	)

	// Create dialog
	dialog := dialog.NewCustomConfirm(
		"Add Connection",
		"Add",
		"Cancel",
		form,
		func(confirmed bool) {
			if confirmed {
				ui.addConnection(nameEntry.Text, hostEntry.Text, portEntry.Text,
					databaseEntry.Text, usernameEntry.Text, passwordEntry.Text,
					sslModeSelect.Selected)
			}
		},
		fyne.CurrentApp().Driver().AllWindows()[0],
	)

	dialog.Resize(fyne.NewSize(400, 500))
	dialog.Show()
}

// addConnection adds a new database connection
func (ui *UI) addConnection(name, host, port, database, username, password, sslMode string) {
	portInt, err := strconv.Atoi(port)
	if err != nil {
		ui.updateStatus("Error: Invalid port number")
		return
	}

	connCfg := config.DatabaseConnection{
		Name:     name,
		Host:     host,
		Port:     portInt,
		Database: database,
		Username: username,
		Password: password,
		SSLMode:  sslMode,
		Enabled:  true,
	}

	if err := ui.manager.AddConnection(connCfg); err != nil {
		ui.updateStatus(fmt.Sprintf("Error adding connection: %s", err.Error()))
		return
	}

	ui.refreshConnections()
	ui.updateStatus(fmt.Sprintf("Connection '%s' added successfully", name))
}

// testSelectedConnection tests the selected database connection
func (ui *UI) testSelectedConnection() {
	if ui.selectedConnection == "" {
		ui.updateStatus("Error: No connection selected")
		return
	}

	conn, err := ui.manager.GetConnection(ui.selectedConnection)
	if err != nil {
		ui.updateStatus(fmt.Sprintf("Error: %s", err.Error()))
		return
	}

	// Test connection by checking health
	conn.CheckHealth()
	status := conn.GetStatus()

	if status.Connected {
		ui.updateStatus(fmt.Sprintf("Connection '%s' test successful (latency: %v)",
			ui.selectedConnection, status.Latency))
	} else {
		ui.updateStatus(fmt.Sprintf("Connection '%s' test failed: %s",
			ui.selectedConnection, status.Error.Error()))
	}
}

// executeQuery executes the SQL query in the editor
func (ui *UI) executeQuery() {
	if ui.selectedConnection == "" {
		ui.updateStatus("Error: No connection selected")
		return
	}

	query := strings.TrimSpace(ui.queryEditor.Text)
	if query == "" {
		ui.updateStatus("Error: No query entered")
		return
	}

	// Add to history
	ui.queryHistory = append(ui.queryHistory, query)

	// Execute query
	ui.updateStatus("Executing query...")

	result, err := ui.manager.ExecuteQuery(ui.selectedConnection, query)
	if err != nil {
		ui.updateStatus(fmt.Sprintf("Query error: %s", err.Error()))
		return
	}

	// Update result table
	ui.updateResultTable(result)

	ui.updateStatus(fmt.Sprintf("Query executed successfully (%d rows, %v)",
		result.RowCount, result.Duration))
}

// updateResultTable updates the result table with query results
func (ui *UI) updateResultTable(result *QueryResult) {
	if result == nil || len(result.Columns) == 0 {
		return
	}

	// Update table structure
	ui.resultTable.Length = func() (int, int) {
		return len(result.Rows) + 1, len(result.Columns) // +1 for header
	}

	ui.resultTable.CreateCell = func() fyne.CanvasObject {
		return widget.NewLabel("")
	}

	ui.resultTable.UpdateCell = func(id widget.TableCellID, obj fyne.CanvasObject) {
		label := obj.(*widget.Label)

		if id.Row == 0 {
			// Header row
			if id.Col < len(result.Columns) {
				label.SetText(result.Columns[id.Col])
			}
		} else {
			// Data row
			rowIndex := id.Row - 1
			if rowIndex < len(result.Rows) && id.Col < len(result.Rows[rowIndex]) {
				value := result.Rows[rowIndex][id.Col]
				if value != nil {
					label.SetText(fmt.Sprintf("%v", value))
				} else {
					label.SetText("NULL")
				}
			}
		}
	}

	ui.resultTable.Refresh()
}

// refreshConnections refreshes the connection list
func (ui *UI) refreshConnections() {
	ui.connectionList.Refresh()
	ui.updateStatus("Connection list refreshed")
}

// showQueryHistory shows the query history dialog
func (ui *UI) showQueryHistory() {
	if len(ui.queryHistory) == 0 {
		ui.updateStatus("No query history available")
		return
	}

	historyList := widget.NewList(
		func() int { return len(ui.queryHistory) },
		func() fyne.CanvasObject { return widget.NewLabel("") },
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			if id < len(ui.queryHistory) {
				label := obj.(*widget.Label)
				query := ui.queryHistory[len(ui.queryHistory)-1-id] // Reverse order
				if len(query) > 50 {
					query = query[:50] + "..."
				}
				label.SetText(query)
			}
		},
	)

	historyList.OnSelected = func(id widget.ListItemID) {
		if id < len(ui.queryHistory) {
			selectedQuery := ui.queryHistory[len(ui.queryHistory)-1-id]
			ui.queryEditor.SetText(selectedQuery)
		}
	}

	dialog := dialog.NewCustom("Query History", "Close",
		container.NewScroll(historyList),
		fyne.CurrentApp().Driver().AllWindows()[0])
	dialog.Resize(fyne.NewSize(600, 400))
	dialog.Show()
}

// updateStatus updates the status label
func (ui *UI) updateStatus(message string) {
	ui.statusLabel.SetText(fmt.Sprintf(">>> %s <<<", message))
}

// Content returns the UI content
func (ui *UI) Content() fyne.CanvasObject {
	return ui.content
}

// Refresh refreshes the UI
func (ui *UI) Refresh() {
	ui.refreshConnections()
	if ui.content != nil {
		ui.content.Refresh()
	}
}
