// Package k8s provides the UI components for Kubernetes management
// Implements cyberpunk-styled cluster interface with real-time monitoring
package k8s

import (
	"fmt"
	"strings"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/widget"

	"assistant-go/internal/config"
	"assistant-go/pkg/cybertheme"
)

// UI provides the user interface for Kubernetes management
type UI struct {
	manager *Manager
	config  *config.Config
	theme   *cybertheme.CyberTheme

	// Main components
	content         *container.Scroll
	contextList     *widget.List
	namespaceSelect *widget.Select
	resourceTable   *widget.Table
	statusLabel     *widget.Label

	// Current state
	selectedContext   string
	selectedNamespace string
	currentResources  []ResourceInfo
	resourceType      string
}

// NewUI creates a new Kubernetes UI
func NewUI(manager *Manager, cfg *config.Config, theme *cybertheme.CyberTheme) *UI {
	ui := &UI{
		manager:      manager,
		config:       cfg,
		theme:        theme,
		resourceType: "pods", // Default to pods
	}

	ui.createComponents()
	return ui
}

// createComponents creates all UI components
func (ui *UI) createComponents() {
	// Create context list
	ui.createContextList()

	// Create namespace selector
	ui.createNamespaceSelector()

	// Create resource table
	ui.createResourceTable()

	// Create status label
	ui.statusLabel = widget.NewLabel(">>> KUBERNETES MODULE READY <<<")

	// Create main layout
	ui.createMainLayout()
}

// createContextList creates the context management interface
func (ui *UI) createContextList() {
	contexts := ui.manager.ListContexts()
	contextNames := make([]string, 0, len(contexts))

	for name := range contexts {
		contextNames = append(contextNames, name)
	}

	ui.contextList = widget.NewList(
		func() int {
			return len(contextNames)
		},
		func() fyne.CanvasObject {
			return container.NewHBox(
				widget.NewIcon(nil), // TODO: Add context status icon
				widget.NewLabel("Context Name"),
				widget.NewLabel("Status"),
				widget.NewLabel("Nodes"),
			)
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			if id < len(contextNames) {
				name := contextNames[id]
				client, _ := ui.manager.GetContext(name)

				cont := obj.(*fyne.Container)
				nameLabel := cont.Objects[1].(*widget.Label)
				statusLabel := cont.Objects[2].(*widget.Label)
				nodesLabel := cont.Objects[3].(*widget.Label)

				nameLabel.SetText(name)

				if client != nil {
					status := client.GetStatus()
					if status.Connected {
						statusLabel.SetText("🟢 Connected")
						nodesLabel.SetText(fmt.Sprintf("%d nodes", status.NodeCount))
					} else {
						statusLabel.SetText("🔴 Disconnected")
						nodesLabel.SetText("N/A")
					}
				}
			}
		},
	)

	ui.contextList.OnSelected = func(id widget.ListItemID) {
		if id < len(contextNames) {
			ui.selectedContext = contextNames[id]
			ui.manager.SetActiveContext(ui.selectedContext)
			ui.updateNamespaces()
			ui.updateStatus(fmt.Sprintf("Selected context: %s", ui.selectedContext))
		}
	}
}

// createNamespaceSelector creates the namespace selection widget
func (ui *UI) createNamespaceSelector() {
	ui.namespaceSelect = widget.NewSelect([]string{}, func(selected string) {
		ui.selectedNamespace = selected
		ui.refreshResources()
		ui.updateStatus(fmt.Sprintf("Selected namespace: %s", selected))
	})
	ui.namespaceSelect.PlaceHolder = "Select namespace..."
}

// createResourceTable creates the resource display table
func (ui *UI) createResourceTable() {
	ui.resourceTable = widget.NewTable(
		func() (int, int) {
			if len(ui.currentResources) == 0 {
				return 1, 5 // Header only
			}
			return len(ui.currentResources) + 1, 5 // +1 for header
		},
		func() fyne.CanvasObject {
			return widget.NewLabel("")
		},
		func(id widget.TableCellID, obj fyne.CanvasObject) {
			label := obj.(*widget.Label)

			if id.Row == 0 {
				// Header row
				headers := []string{"Name", "Namespace", "Status", "Age", "Labels"}
				if id.Col < len(headers) {
					label.SetText(headers[id.Col])
				}
			} else {
				// Data row
				resourceIndex := id.Row - 1
				if resourceIndex < len(ui.currentResources) {
					resource := ui.currentResources[resourceIndex]
					switch id.Col {
					case 0:
						label.SetText(resource.Name)
					case 1:
						label.SetText(resource.Namespace)
					case 2:
						label.SetText(resource.Status)
					case 3:
						label.SetText(formatAge(resource.Age))
					case 4:
						label.SetText(formatLabels(resource.Labels))
					}
				}
			}
		},
	)
}

// createMainLayout creates the main UI layout
func (ui *UI) createMainLayout() {
	// Create context management section
	contextSection := container.NewVBox(
		widget.NewLabel("☸️ Kubernetes Contexts"),
		widget.NewSeparator(),
		ui.contextList,
		container.NewHBox(
			widget.NewButton("Add Context", ui.showAddContextDialog),
			widget.NewButton("Test Connection", ui.testSelectedContext),
			widget.NewButton("Refresh", ui.refreshContexts),
		),
	)

	// Create resource browser section
	resourceSection := container.NewVBox(
		widget.NewLabel("📋 Resource Browser"),
		widget.NewSeparator(),
		container.NewHBox(
			widget.NewLabel("Namespace:"),
			ui.namespaceSelect,
		),
		container.NewHBox(
			widget.NewButton("Pods", func() { ui.setResourceType("pods") }),
			widget.NewButton("Services", func() { ui.setResourceType("services") }),
			widget.NewButton("Deployments", func() { ui.setResourceType("deployments") }),
			widget.NewButton("Refresh", ui.refreshResources),
		),
		container.NewScroll(ui.resourceTable),
	)

	// Create main container with scrolling support
	mainContainer := container.NewVBox(
		widget.NewRichTextFromMarkdown(`
# ☸️ Kubernetes Commander

**VISUAL CLUSTER MANAGEMENT**

> Transform cluster management into an intuitive visual experience.
> Real-time monitoring, drag-and-drop resource management, and
> AI-assisted troubleshooting make complex operations simple.

---
`),
		contextSection,
		widget.NewSeparator(),
		resourceSection,
		widget.NewSeparator(),
		ui.statusLabel,
	)

	// Wrap in scroll container to handle overflow
	ui.content = container.NewScroll(mainContainer)
	ui.content.SetMinSize(fyne.NewSize(800, 600))
}

// showAddContextDialog shows the add context dialog
func (ui *UI) showAddContextDialog() {
	// Create form fields
	nameEntry := widget.NewEntry()
	nameEntry.SetPlaceHolder("Context name")

	configPathEntry := widget.NewEntry()
	configPathEntry.SetPlaceHolder("~/.kube/config")
	configPathEntry.SetText("~/.kube/config")

	namespaceEntry := widget.NewEntry()
	namespaceEntry.SetPlaceHolder("default")
	namespaceEntry.SetText("default")

	// Create form
	form := container.NewVBox(
		widget.NewLabel("Add Kubernetes Context"),
		widget.NewSeparator(),
		widget.NewForm(
			widget.NewFormItem("Name", nameEntry),
			widget.NewFormItem("Config Path", configPathEntry),
			widget.NewFormItem("Default Namespace", namespaceEntry),
		),
	)

	// Create dialog
	dialog := dialog.NewCustomConfirm(
		"Add Context",
		"Add",
		"Cancel",
		form,
		func(confirmed bool) {
			if confirmed {
				ui.addContext(nameEntry.Text, configPathEntry.Text, namespaceEntry.Text)
			}
		},
		fyne.CurrentApp().Driver().AllWindows()[0],
	)

	dialog.Resize(fyne.NewSize(400, 300))
	dialog.Show()
}

// addContext adds a new Kubernetes context
func (ui *UI) addContext(name, configPath, namespace string) {
	if name == "" {
		ui.updateStatus("Error: Context name cannot be empty")
		return
	}

	contextCfg := config.K8sContext{
		Name:       name,
		ConfigPath: configPath,
		Namespace:  namespace,
		Enabled:    true,
	}

	if err := ui.manager.AddContext(contextCfg); err != nil {
		ui.updateStatus(fmt.Sprintf("Error adding context: %s", err.Error()))
		return
	}

	ui.refreshContexts()
	ui.updateStatus(fmt.Sprintf("Context '%s' added successfully", name))
}

// testSelectedContext tests the selected Kubernetes context
func (ui *UI) testSelectedContext() {
	if ui.selectedContext == "" {
		ui.updateStatus("Error: No context selected")
		return
	}

	client, err := ui.manager.GetContext(ui.selectedContext)
	if err != nil {
		ui.updateStatus(fmt.Sprintf("Error: %s", err.Error()))
		return
	}

	// Test connection by checking health
	client.CheckHealth()
	status := client.GetStatus()

	if status.Connected {
		ui.updateStatus(fmt.Sprintf("Context '%s' test successful (%s, %d nodes)",
			ui.selectedContext, status.Version, status.NodeCount))
	} else {
		ui.updateStatus(fmt.Sprintf("Context '%s' test failed: %s",
			ui.selectedContext, status.Error.Error()))
	}
}

// updateNamespaces updates the namespace selector
func (ui *UI) updateNamespaces() {
	if ui.selectedContext == "" {
		return
	}

	namespaces, err := ui.manager.ListNamespaces()
	if err != nil {
		ui.updateStatus(fmt.Sprintf("Error loading namespaces: %s", err.Error()))
		return
	}

	ui.namespaceSelect.Options = namespaces
	if len(namespaces) > 0 {
		ui.namespaceSelect.SetSelected(namespaces[0])
		ui.selectedNamespace = namespaces[0]
	}

	ui.namespaceSelect.Refresh()
}

// setResourceType sets the current resource type to display
func (ui *UI) setResourceType(resourceType string) {
	ui.resourceType = resourceType
	ui.refreshResources()
	ui.updateStatus(fmt.Sprintf("Viewing %s", resourceType))
}

// refreshResources refreshes the resource table
func (ui *UI) refreshResources() {
	if ui.selectedContext == "" || ui.selectedNamespace == "" {
		ui.currentResources = []ResourceInfo{}
		ui.resourceTable.Refresh()
		return
	}

	var resources []ResourceInfo
	var err error

	switch ui.resourceType {
	case "pods":
		resources, err = ui.manager.ListPods(ui.selectedNamespace)
	case "services":
		resources, err = ui.manager.ListServices(ui.selectedNamespace)
	default:
		ui.updateStatus(fmt.Sprintf("Resource type '%s' not yet implemented", ui.resourceType))
		return
	}

	if err != nil {
		ui.updateStatus(fmt.Sprintf("Error loading %s: %s", ui.resourceType, err.Error()))
		return
	}

	ui.currentResources = resources
	ui.resourceTable.Refresh()

	ui.updateStatus(fmt.Sprintf("Loaded %d %s from namespace %s",
		len(resources), ui.resourceType, ui.selectedNamespace))
}

// refreshContexts refreshes the context list
func (ui *UI) refreshContexts() {
	ui.contextList.Refresh()
	ui.updateStatus("Context list refreshed")
}

// updateStatus updates the status label
func (ui *UI) updateStatus(message string) {
	ui.statusLabel.SetText(fmt.Sprintf(">>> %s <<<", message))
}

// formatAge formats a duration as a human-readable age
func formatAge(age time.Duration) string {
	if age < time.Minute {
		return fmt.Sprintf("%ds", int(age.Seconds()))
	} else if age < time.Hour {
		return fmt.Sprintf("%dm", int(age.Minutes()))
	} else if age < 24*time.Hour {
		return fmt.Sprintf("%dh", int(age.Hours()))
	} else {
		return fmt.Sprintf("%dd", int(age.Hours()/24))
	}
}

// formatLabels formats labels as a string
func formatLabels(labels map[string]string) string {
	if len(labels) == 0 {
		return "none"
	}

	var parts []string
	count := 0
	for key, value := range labels {
		if count >= 2 { // Limit to 2 labels for display
			parts = append(parts, "...")
			break
		}
		parts = append(parts, fmt.Sprintf("%s=%s", key, value))
		count++
	}

	return strings.Join(parts, ", ")
}

// Content returns the UI content
func (ui *UI) Content() fyne.CanvasObject {
	return ui.content
}

// Refresh refreshes the UI
func (ui *UI) Refresh() {
	ui.refreshContexts()
	if ui.selectedContext != "" {
		ui.updateNamespaces()
		ui.refreshResources()
	}
	if ui.content != nil {
		ui.content.Refresh()
	}
}
