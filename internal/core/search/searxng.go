// Package search provides SearXNG client implementation
// searxng.go - SearXNG API client
package search

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"assistant-go/internal/config"
)

// SearXNGClient handles communication with SearXNG instances
type SearXNGClient struct {
	baseURL    string
	httpClient *http.Client
	config     config.SearchConfig
}

// SearchOptions represents search options
type SearchOptions struct {
	Categories  []string `json:"categories,omitempty"`
	Engines     []string `json:"engines,omitempty"`
	Language    string   `json:"language,omitempty"`
	SafeSearch  int      `json:"safesearch,omitempty"`
	TimeRange   string   `json:"time_range,omitempty"`
	Format      string   `json:"format,omitempty"`
	MaxResults  int      `json:"max_results,omitempty"`
}

// SearXNGResponse represents the response from SearXNG API
type SearXNGResponse struct {
	Query             string                   `json:"query"`
	NumberOfResults   int                      `json:"number_of_results"`
	Results           []SearXNGResult          `json:"results"`
	Answers           []string                 `json:"answers,omitempty"`
	Corrections       []string                 `json:"corrections,omitempty"`
	Infoboxes         []map[string]interface{} `json:"infoboxes,omitempty"`
	Suggestions       []string                 `json:"suggestions,omitempty"`
	UnresponsiveEngines []string               `json:"unresponsive_engines,omitempty"`
}

// SearXNGResult represents a single result from SearXNG
type SearXNGResult struct {
	URL         string    `json:"url"`
	Title       string    `json:"title"`
	Content     string    `json:"content"`
	Engine      string    `json:"engine"`
	ParsedURL   []string  `json:"parsed_url"`
	Template    string    `json:"template,omitempty"`
	Engines     []string  `json:"engines"`
	Positions   []int     `json:"positions"`
	Score       float64   `json:"score"`
	Category    string    `json:"category"`
	PublishedDate *time.Time `json:"publishedDate,omitempty"`
	Thumbnail   string    `json:"thumbnail,omitempty"`
}

// NewSearXNGClient creates a new SearXNG client
func NewSearXNGClient(config config.SearchConfig) (*SearXNGClient, error) {
	if config.BaseURL == "" {
		return nil, fmt.Errorf("SearXNG base URL is required")
	}

	// Validate URL
	if _, err := url.Parse(config.BaseURL); err != nil {
		return nil, fmt.Errorf("invalid SearXNG base URL: %w", err)
	}

	client := &SearXNGClient{
		baseURL: strings.TrimSuffix(config.BaseURL, "/"),
		httpClient: &http.Client{
			Timeout: config.Timeout,
		},
		config: config,
	}

	return client, nil
}

// Start starts the SearXNG client
func (c *SearXNGClient) Start() error {
	// No specific startup required for HTTP client
	return nil
}

// Stop stops the SearXNG client
func (c *SearXNGClient) Stop() {
	// Close HTTP client if needed
	c.httpClient.CloseIdleConnections()
}

// TestConnection tests connectivity to SearXNG instance
func (c *SearXNGClient) TestConnection() error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Try to get search engines list as a connectivity test
	req, err := http.NewRequestWithContext(ctx, "GET", c.baseURL+"/config", nil)
	if err != nil {
		return fmt.Errorf("failed to create test request: %w", err)
	}

	// Add custom headers if configured
	for key, value := range c.config.CustomHeaders {
		req.Header.Set(key, value)
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to connect to SearXNG: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("SearXNG returned status %d", resp.StatusCode)
	}

	return nil
}

// Search performs a search query
func (c *SearXNGClient) Search(query string, categories []string) (*SearchResult, error) {
	options := SearchOptions{
		Categories: categories,
		Language:   c.config.Language,
		SafeSearch: c.config.SafeSearch,
		MaxResults: c.config.MaxResults,
		Format:     "json",
	}

	return c.SearchWithOptions(query, options)
}

// SearchWithOptions performs a search with custom options
func (c *SearXNGClient) SearchWithOptions(query string, options SearchOptions) (*SearchResult, error) {
	if query == "" {
		return nil, fmt.Errorf("search query cannot be empty")
	}

	// Build search URL
	searchURL, err := c.buildSearchURL(query, options)
	if err != nil {
		return nil, fmt.Errorf("failed to build search URL: %w", err)
	}

	// Create request
	ctx, cancel := context.WithTimeout(context.Background(), c.config.Timeout)
	defer cancel()

	req, err := http.NewRequestWithContext(ctx, "GET", searchURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create search request: %w", err)
	}

	// Add custom headers
	for key, value := range c.config.CustomHeaders {
		req.Header.Set(key, value)
	}

	// Set User-Agent to identify as a legitimate client
	req.Header.Set("User-Agent", "Assistant-Go/1.0 (Development Tool)")
	req.Header.Set("Accept", "application/json")

	// Perform request
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("search request failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("search failed with status %d: %s", resp.StatusCode, string(body))
	}

	// Parse response
	var searxngResp SearXNGResponse
	if err := json.NewDecoder(resp.Body).Decode(&searxngResp); err != nil {
		return nil, fmt.Errorf("failed to parse search response: %w", err)
	}

	// Convert to our format
	result := &SearchResult{
		Query:     query,
		Results:   make([]Result, len(searxngResp.Results)),
		Engines:   c.extractEngines(searxngResp.Results),
		Timestamp: time.Now(),
		Total:     searxngResp.NumberOfResults,
	}

	for i, r := range searxngResp.Results {
		result.Results[i] = Result{
			Title:     r.Title,
			URL:       r.URL,
			Content:   r.Content,
			Engine:    r.Engine,
			Category:  r.Category,
			Score:     r.Score,
			Thumbnail: r.Thumbnail,
		}

		if r.PublishedDate != nil {
			result.Results[i].PublishedAt = *r.PublishedDate
		}
	}

	return result, nil
}

// GetSuggestions gets search suggestions
func (c *SearXNGClient) GetSuggestions(query string) ([]string, error) {
	if query == "" {
		return []string{}, nil
	}

	// Build suggestions URL
	suggestURL := fmt.Sprintf("%s/autocompleter?q=%s", c.baseURL, url.QueryEscape(query))

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	req, err := http.NewRequestWithContext(ctx, "GET", suggestURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create suggestions request: %w", err)
	}

	// Add custom headers
	for key, value := range c.config.CustomHeaders {
		req.Header.Set(key, value)
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("suggestions request failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return []string{}, nil // Return empty suggestions on error
	}

	var suggestions []string
	if err := json.NewDecoder(resp.Body).Decode(&suggestions); err != nil {
		return []string{}, nil // Return empty suggestions on parse error
	}

	return suggestions, nil
}

// GetEngines returns available search engines
func (c *SearXNGClient) GetEngines() ([]string, error) {
	// This would require parsing the SearXNG config or stats endpoint
	// For now, return common engines
	return []string{
		"google", "bing", "duckduckgo", "startpage", "qwant",
		"github", "stackoverflow", "wikipedia", "arxiv",
	}, nil
}

// GetCategories returns available search categories
func (c *SearXNGClient) GetCategories() ([]string, error) {
	// Return standard SearXNG categories
	return []string{
		"general", "images", "videos", "news", "map", "music",
		"it", "science", "files", "social media",
	}, nil
}

// buildSearchURL builds the search URL with parameters
func (c *SearXNGClient) buildSearchURL(query string, options SearchOptions) (string, error) {
	baseURL := c.baseURL + "/search"
	
	params := url.Values{}
	params.Set("q", query)
	params.Set("format", "json")

	// Set categories
	if len(options.Categories) > 0 {
		params.Set("categories", strings.Join(options.Categories, ","))
	} else if len(c.config.Categories) > 0 {
		params.Set("categories", strings.Join(c.config.Categories, ","))
	}

	// Set engines
	if len(options.Engines) > 0 {
		params.Set("engines", strings.Join(options.Engines, ","))
	} else if len(c.config.Engines) > 0 {
		params.Set("engines", strings.Join(c.config.Engines, ","))
	}

	// Set language
	language := options.Language
	if language == "" {
		language = c.config.Language
	}
	if language != "" {
		params.Set("language", language)
	}

	// Set safe search
	safeSearch := options.SafeSearch
	if safeSearch == 0 {
		safeSearch = c.config.SafeSearch
	}
	params.Set("safesearch", strconv.Itoa(safeSearch))

	// Set time range
	if options.TimeRange != "" {
		params.Set("time_range", options.TimeRange)
	}

	return baseURL + "?" + params.Encode(), nil
}

// extractEngines extracts unique engines from results
func (c *SearXNGClient) extractEngines(results []SearXNGResult) []string {
	engineSet := make(map[string]bool)
	for _, result := range results {
		if result.Engine != "" {
			engineSet[result.Engine] = true
		}
		for _, engine := range result.Engines {
			engineSet[engine] = true
		}
	}

	engines := make([]string, 0, len(engineSet))
	for engine := range engineSet {
		engines = append(engines, engine)
	}

	return engines
}

// TODO: Implement search result caching
// TODO: Implement search result filtering and ranking
// TODO: Implement custom search engine configuration
// TODO: Implement search statistics and analytics
// TODO: Implement search result export functionality
