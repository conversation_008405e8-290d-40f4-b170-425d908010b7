// Package docker provides Docker container management functionality
// manager.go - Main Docker module manager
package docker

import (
	"context"
	"fmt"
	"log/slog"
	"sync"
	"time"

	"assistant-go/internal/types"
	"assistant-go/internal/config"
)

// Manager handles Docker operations using domain-specific managers
type Manager struct {
	config *config.Config
	logger *slog.Logger

	// Domain-specific managers
	containerManager *ContainerManager
	imageManager     *ImageManager
	networkManager   *NetworkManager
	volumeManager    *VolumeManager
	composeManager   *ComposeManager

	// Module state
	ctx     context.Context
	cancel  context.CancelFunc
	running bool
	mu      sync.RWMutex
}

// NewManager creates a new Docker manager with domain-specific structure
func NewManager() *Manager {
	ctx, cancel := context.WithCancel(context.Background())

	containerManager := NewContainerManager()
	imageManager := NewImageManager()
	networkManager := NewNetworkManager()
	volumeManager := NewVolumeManager()
	composeManager := NewComposeManager()

	return &Manager{
		containerManager: containerManager,
		imageManager:     imageManager,
		networkManager:   networkManager,
		volumeManager:    volumeManager,
		composeManager:   composeManager,
		ctx:              ctx,
		cancel:           cancel,
		logger:           slog.Default().With("component", "docker"),
	}
}

// Initialize implements the Module interface
func (m *Manager) Initialize(ctx context.Context, cfg *config.Config) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.config = cfg
	m.logger.Info("Initializing Docker module")

	// Initialize all sub-managers
	if err := m.containerManager.Initialize(ctx, cfg); err != nil {
		return fmt.Errorf("failed to initialize container manager: %w", err)
	}

	if err := m.imageManager.Initialize(ctx, cfg); err != nil {
		return fmt.Errorf("failed to initialize image manager: %w", err)
	}

	if err := m.networkManager.Initialize(ctx, cfg); err != nil {
		return fmt.Errorf("failed to initialize network manager: %w", err)
	}

	if err := m.volumeManager.Initialize(ctx, cfg); err != nil {
		return fmt.Errorf("failed to initialize volume manager: %w", err)
	}

	if err := m.composeManager.Initialize(ctx, cfg); err != nil {
		return fmt.Errorf("failed to initialize compose manager: %w", err)
	}

	m.logger.Info("Docker module initialized successfully")
	return nil
}

// Start implements the Module interface
func (m *Manager) Start(ctx context.Context) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.running {
		return fmt.Errorf("Docker module is already running")
	}

	m.logger.Info("Starting Docker module")

	// Start all sub-managers
	if err := m.containerManager.Start(); err != nil {
		return fmt.Errorf("failed to start container manager: %w", err)
	}

	if err := m.imageManager.Start(); err != nil {
		return fmt.Errorf("failed to start image manager: %w", err)
	}

	if err := m.networkManager.Start(); err != nil {
		return fmt.Errorf("failed to start network manager: %w", err)
	}

	if err := m.volumeManager.Start(); err != nil {
		return fmt.Errorf("failed to start volume manager: %w", err)
	}

	m.running = true
	m.logger.Info("Docker module started successfully")
	return nil
}

// Stop implements the Module interface
func (m *Manager) Stop(ctx context.Context) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if !m.running {
		return nil
	}

	m.logger.Info("Stopping Docker module")

	// Stop all sub-managers
	m.containerManager.Stop()
	m.imageManager.Stop()
	m.networkManager.Stop()
	m.volumeManager.Stop()

	m.cancel()
	m.running = false

	m.logger.Info("Docker module stopped")
	return nil
}

// Health implements the Module interface
func (m *Manager) Health() types.ModuleHealth {
	m.mu.RLock()
	defer m.mu.RUnlock()

	health := types.ModuleHealth{
		Status:    "healthy",
		Message:   "Docker daemon connected",
		LastCheck: time.Now(),
	}

	if !m.running {
		health.Status = "unhealthy"
		health.Message = "Module not running"
		return health
	}

	// Check Docker daemon connectivity
	if !m.containerManager.IsDockerAvailable() {
		health.Status = "unhealthy"
		health.Message = "Docker daemon not available"
		return health
	}

	// Get summary information
	containerCount := m.containerManager.GetContainerCount()
	imageCount := m.imageManager.GetImageCount()
	networkCount := m.networkManager.GetNetworkCount()
	volumeCount := m.volumeManager.GetVolumeCount()

	health.Message = fmt.Sprintf("%d containers, %d images, %d networks, %d volumes",
		containerCount, imageCount, networkCount, volumeCount)

	return health
}

// Name implements the Module interface
func (m *Manager) Name() string {
	return "Docker"
}

// GetContainerManager returns the container manager
func (m *Manager) GetContainerManager() *ContainerManager {
	return m.containerManager
}

// GetImageManager returns the image manager
func (m *Manager) GetImageManager() *ImageManager {
	return m.imageManager
}

// GetNetworkManager returns the network manager
func (m *Manager) GetNetworkManager() *NetworkManager {
	return m.networkManager
}

// GetVolumeManager returns the volume manager
func (m *Manager) GetVolumeManager() *VolumeManager {
	return m.volumeManager
}

// GetDockerInfo returns Docker system information
func (m *Manager) GetDockerInfo() (map[string]interface{}, error) {
	return m.containerManager.GetDockerInfo()
}

// GetDockerVersion returns Docker version information
func (m *Manager) GetDockerVersion() (map[string]interface{}, error) {
	return m.containerManager.GetDockerVersion()
}

// ExecuteCommand executes a command in a running container
func (m *Manager) ExecuteCommand(containerID string, cmd []string) (string, error) {
	return m.containerManager.ExecuteCommand(containerID, cmd)
}

// GetContainerLogs retrieves logs from a container
func (m *Manager) GetContainerLogs(containerID string, lines int) (string, error) {
	return m.containerManager.GetLogs(containerID, lines)
}

// ListContainers returns all containers
func (m *Manager) ListContainers(all bool) ([]ContainerInfo, error) {
	return m.containerManager.ListContainers(all)
}

// ListImages returns all images
func (m *Manager) ListImages() ([]ImageInfo, error) {
	return m.imageManager.ListImages()
}

// ListNetworks returns all networks
func (m *Manager) ListNetworks() ([]NetworkInfo, error) {
	return m.networkManager.ListNetworks()
}

// ListVolumes returns all volumes
func (m *Manager) ListVolumes() ([]VolumeInfo, error) {
	return m.volumeManager.ListVolumes()
}

// StartContainer starts a container
func (m *Manager) StartContainer(containerID string) error {
	return m.containerManager.StartContainer(containerID)
}

// StopContainer stops a container
func (m *Manager) StopContainer(containerID string) error {
	return m.containerManager.StopContainer(containerID)
}

// RestartContainer restarts a container
func (m *Manager) RestartContainer(containerID string) error {
	return m.containerManager.RestartContainer(containerID)
}

// RemoveContainer removes a container
func (m *Manager) RemoveContainer(containerID string, force bool) error {
	return m.containerManager.RemoveContainer(containerID, force)
}

// PullImage pulls a Docker image
func (m *Manager) PullImage(imageName string) error {
	return m.imageManager.PullImage(imageName)
}

// RemoveImage removes a Docker image
func (m *Manager) RemoveImage(imageID string, force bool) error {
	return m.imageManager.RemoveImage(imageID, force)
}

// CreateNetwork creates a Docker network
func (m *Manager) CreateNetwork(name string, driver string) error {
	return m.networkManager.CreateNetwork(name, driver)
}

// RemoveNetwork removes a Docker network
func (m *Manager) RemoveNetwork(networkID string) error {
	return m.networkManager.RemoveNetwork(networkID)
}

// CreateVolume creates a Docker volume
func (m *Manager) CreateVolume(name string) error {
	return m.volumeManager.CreateVolume(name)
}

// RemoveVolume removes a Docker volume
func (m *Manager) RemoveVolume(volumeName string, force bool) error {
	return m.volumeManager.RemoveVolume(volumeName, force)
}

// GetContainerStats returns real-time stats for a container
func (m *Manager) GetContainerStats(containerID string) (ContainerStats, error) {
	return m.containerManager.GetStats(containerID)
}

// InspectContainer returns detailed information about a container
func (m *Manager) InspectContainer(containerID string) (map[string]interface{}, error) {
	return m.containerManager.InspectContainer(containerID)
}

// InspectImage returns detailed information about an image
func (m *Manager) InspectImage(imageID string) (map[string]interface{}, error) {
	return m.imageManager.InspectImage(imageID)
}

// GetImageHistory returns the history of an image
func (m *Manager) GetImageHistory(imageID string) ([]map[string]interface{}, error) {
	return m.imageManager.GetHistory(imageID)
}

// PruneContainers removes all stopped containers
func (m *Manager) PruneContainers() error {
	return m.containerManager.PruneContainers()
}

// PruneImages removes unused images
func (m *Manager) PruneImages() error {
	return m.imageManager.PruneImages()
}

// PruneNetworks removes unused networks
func (m *Manager) PruneNetworks() error {
	return m.networkManager.PruneNetworks()
}

// PruneVolumes removes unused volumes
func (m *Manager) PruneVolumes() error {
	return m.volumeManager.PruneVolumes()
}

// TODO: Implement Docker Compose support
// TODO: Implement container monitoring and alerts
// TODO: Implement image vulnerability scanning
// TODO: Implement container resource limits management
// TODO: Implement Docker registry management
// TODO: Implement container backup and restore
// TODO: Implement multi-host Docker Swarm support
