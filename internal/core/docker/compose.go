// Package docker provides Docker Compose management functionality
// compose.go - Handles Docker Compose operations for multi-container applications
package docker

import (
	"context"
	"fmt"
	"log/slog"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"assistant-go/internal/config"
)

// ComposeManager handles Docker Compose operations
type ComposeManager struct {
	logger *slog.Logger
	mu     sync.RWMutex

	// Compose projects
	projects map[string]*ComposeProject
}

// ComposeProject represents a Docker Compose project
type ComposeProject struct {
	Name        string
	Path        string
	ComposeFile string
	Services    []ComposeService
	Status      string
	LastUpdated time.Time
}

// ComposeService represents a service in a Docker Compose project
type ComposeService struct {
	Name      string
	Image     string
	Status    string
	Ports     []string
	Replicas  int
	Health    string
}

// ComposeStats represents statistics for a Compose project
type ComposeStats struct {
	TotalServices   int
	RunningServices int
	StoppedServices int
	FailedServices  int
	Uptime          time.Duration
}

// NewComposeManager creates a new Docker Compose manager
func NewComposeManager() *ComposeManager {
	return &ComposeManager{
		logger:   slog.Default().With("component", "docker-compose"),
		projects: make(map[string]*ComposeProject),
	}
}

// Initialize initializes the Compose manager
func (cm *ComposeManager) Initialize(ctx context.Context, cfg *config.Config) error {
	cm.logger.Info("Initializing Docker Compose manager")

	// Check if docker-compose is available
	if !cm.isComposeAvailable() {
		cm.logger.Warn("Docker Compose not available - some features will be disabled")
	}

	// Scan for existing compose projects
	if err := cm.scanForProjects(); err != nil {
		cm.logger.Error("Failed to scan for Compose projects", "error", err)
		// Continue initialization - projects can be added manually
	}

	cm.logger.Info("Docker Compose manager initialized")
	return nil
}

// Start starts the Compose manager
func (cm *ComposeManager) Start() error {
	cm.logger.Info("Starting Docker Compose manager")
	
	// Start monitoring compose projects
	go cm.monitorProjects()
	
	return nil
}

// Stop stops the Compose manager
func (cm *ComposeManager) Stop() {
	cm.logger.Info("Stopping Docker Compose manager")
	// Cleanup resources
}

// isComposeAvailable checks if Docker Compose is available
func (cm *ComposeManager) isComposeAvailable() bool {
	// Try docker compose (new syntax)
	if err := exec.Command("docker", "compose", "version").Run(); err == nil {
		return true
	}
	
	// Try docker-compose (legacy syntax)
	if err := exec.Command("docker-compose", "version").Run(); err == nil {
		return true
	}
	
	return false
}

// scanForProjects scans the current directory for Docker Compose files
func (cm *ComposeManager) scanForProjects() error {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	// Common compose file names
	composeFiles := []string{
		"docker-compose.yml",
		"docker-compose.yaml",
		"compose.yml",
		"compose.yaml",
	}

	// Scan current directory
	for _, filename := range composeFiles {
		if _, err := os.Stat(filename); err == nil {
			project := &ComposeProject{
				Name:        filepath.Base(filename),
				Path:        ".",
				ComposeFile: filename,
				Status:      "unknown",
				LastUpdated: time.Now(),
			}
			
			// Load project details
			if err := cm.loadProjectDetails(project); err != nil {
				cm.logger.Error("Failed to load project details", "file", filename, "error", err)
				continue
			}
			
			cm.projects[project.Name] = project
			cm.logger.Info("Found Compose project", "name", project.Name, "file", filename)
		}
	}

	return nil
}

// loadProjectDetails loads details for a Compose project
func (cm *ComposeManager) loadProjectDetails(project *ComposeProject) error {
	// Get project status
	status, err := cm.getProjectStatus(project)
	if err != nil {
		return fmt.Errorf("failed to get project status: %w", err)
	}
	
	project.Status = status
	
	// Get services
	services, err := cm.getProjectServices(project)
	if err != nil {
		return fmt.Errorf("failed to get project services: %w", err)
	}
	
	project.Services = services
	
	return nil
}

// getProjectStatus gets the status of a Compose project
func (cm *ComposeManager) getProjectStatus(project *ComposeProject) (string, error) {
	cmd := exec.Command("docker", "compose", "-f", project.ComposeFile, "ps", "--format", "json")
	cmd.Dir = project.Path
	
	output, err := cmd.Output()
	if err != nil {
		return "unknown", err
	}
	
	// Parse output to determine overall status
	if len(output) == 0 {
		return "stopped", nil
	}
	
	// Simple status determination - could be enhanced
	if strings.Contains(string(output), "running") {
		return "running", nil
	}
	
	return "partial", nil
}

// getProjectServices gets the services in a Compose project
func (cm *ComposeManager) getProjectServices(project *ComposeProject) ([]ComposeService, error) {
	cmd := exec.Command("docker", "compose", "-f", project.ComposeFile, "config", "--services")
	cmd.Dir = project.Path
	
	output, err := cmd.Output()
	if err != nil {
		return nil, err
	}
	
	serviceNames := strings.Split(strings.TrimSpace(string(output)), "\n")
	services := make([]ComposeService, 0, len(serviceNames))
	
	for _, name := range serviceNames {
		if name == "" {
			continue
		}
		
		service := ComposeService{
			Name:   name,
			Status: "unknown",
		}
		
		// Get service details
		if err := cm.loadServiceDetails(&service, project); err != nil {
			cm.logger.Error("Failed to load service details", "service", name, "error", err)
			continue
		}
		
		services = append(services, service)
	}
	
	return services, nil
}

// loadServiceDetails loads details for a specific service
func (cm *ComposeManager) loadServiceDetails(service *ComposeService, project *ComposeProject) error {
	// Get service status
	cmd := exec.Command("docker", "compose", "-f", project.ComposeFile, "ps", service.Name, "--format", "json")
	cmd.Dir = project.Path
	
	output, err := cmd.Output()
	if err != nil {
		return err
	}
	
	// Parse JSON output to get service details
	// This is a simplified implementation - could be enhanced with proper JSON parsing
	if strings.Contains(string(output), "running") {
		service.Status = "running"
	} else if strings.Contains(string(output), "exited") {
		service.Status = "stopped"
	} else {
		service.Status = "unknown"
	}
	
	return nil
}

// monitorProjects monitors Compose projects for changes
func (cm *ComposeManager) monitorProjects() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()
	
	for range ticker.C {
		cm.refreshProjects()
	}
}

// refreshProjects refreshes the status of all projects
func (cm *ComposeManager) refreshProjects() {
	cm.mu.RLock()
	projects := make([]*ComposeProject, 0, len(cm.projects))
	for _, project := range cm.projects {
		projects = append(projects, project)
	}
	cm.mu.RUnlock()
	
	for _, project := range projects {
		if err := cm.loadProjectDetails(project); err != nil {
			cm.logger.Error("Failed to refresh project", "name", project.Name, "error", err)
		}
	}
}

// ListProjects returns all Compose projects
func (cm *ComposeManager) ListProjects() map[string]*ComposeProject {
	cm.mu.RLock()
	defer cm.mu.RUnlock()
	
	// Return a copy to prevent external modification
	projects := make(map[string]*ComposeProject)
	for name, project := range cm.projects {
		projects[name] = project
	}
	
	return projects
}

// StartProject starts a Compose project
func (cm *ComposeManager) StartProject(projectName string) error {
	cm.mu.RLock()
	project, exists := cm.projects[projectName]
	cm.mu.RUnlock()
	
	if !exists {
		return fmt.Errorf("project %s not found", projectName)
	}
	
	cm.logger.Info("Starting Compose project", "name", projectName)
	
	cmd := exec.Command("docker", "compose", "-f", project.ComposeFile, "up", "-d")
	cmd.Dir = project.Path
	
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("failed to start project: %w", err)
	}
	
	// Refresh project status
	return cm.loadProjectDetails(project)
}

// StopProject stops a Compose project
func (cm *ComposeManager) StopProject(projectName string) error {
	cm.mu.RLock()
	project, exists := cm.projects[projectName]
	cm.mu.RUnlock()
	
	if !exists {
		return fmt.Errorf("project %s not found", projectName)
	}
	
	cm.logger.Info("Stopping Compose project", "name", projectName)
	
	cmd := exec.Command("docker", "compose", "-f", project.ComposeFile, "down")
	cmd.Dir = project.Path
	
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("failed to stop project: %w", err)
	}
	
	// Refresh project status
	return cm.loadProjectDetails(project)
}

// RestartProject restarts a Compose project
func (cm *ComposeManager) RestartProject(projectName string) error {
	if err := cm.StopProject(projectName); err != nil {
		return err
	}
	
	return cm.StartProject(projectName)
}

// GetProjectLogs gets logs for a Compose project
func (cm *ComposeManager) GetProjectLogs(projectName string, lines int) (string, error) {
	cm.mu.RLock()
	project, exists := cm.projects[projectName]
	cm.mu.RUnlock()
	
	if !exists {
		return "", fmt.Errorf("project %s not found", projectName)
	}
	
	cmd := exec.Command("docker", "compose", "-f", project.ComposeFile, "logs", "--tail", fmt.Sprintf("%d", lines))
	cmd.Dir = project.Path
	
	output, err := cmd.Output()
	if err != nil {
		return "", fmt.Errorf("failed to get logs: %w", err)
	}
	
	return string(output), nil
}

// GetProjectStats gets statistics for a Compose project
func (cm *ComposeManager) GetProjectStats(projectName string) (*ComposeStats, error) {
	cm.mu.RLock()
	project, exists := cm.projects[projectName]
	cm.mu.RUnlock()
	
	if !exists {
		return nil, fmt.Errorf("project %s not found", projectName)
	}
	
	stats := &ComposeStats{
		TotalServices: len(project.Services),
	}
	
	for _, service := range project.Services {
		switch service.Status {
		case "running":
			stats.RunningServices++
		case "stopped":
			stats.StoppedServices++
		default:
			stats.FailedServices++
		}
	}
	
	return stats, nil
}
