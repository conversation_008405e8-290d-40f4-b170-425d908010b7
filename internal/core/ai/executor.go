// Package ai provides task execution capabilities
// executor.go - AI-powered task execution
package ai

import (
	"context"
	"fmt"
	"log/slog"
	"sync"
	"time"

	"github.com/tmc/langchaingo/llms"
)

// TaskExecutor handles AI-powered task execution
type TaskExecutor struct {
	llm    llms.Model
	logger *slog.Logger
	
	// Execution state
	runningTasks map[string]*ExecutionContext
	mu           sync.RWMutex
}

// ExecutionContext represents the context of a running task
type ExecutionContext struct {
	TaskID      string
	Plan        *TaskPlan
	CurrentStep int
	Status      string
	StartTime   time.Time
	Results     map[string]string
	Errors      []error
	Cancel      context.CancelFunc
}

// ExecutionResult represents the result of task execution
type ExecutionResult struct {
	TaskID       string                 `json:"task_id"`
	Status       string                 `json:"status"`
	Results      map[string]string      `json:"results"`
	Errors       []string               `json:"errors,omitempty"`
	Duration     time.Duration          `json:"duration"`
	StepsExecuted int                   `json:"steps_executed"`
	Metadata     map[string]interface{} `json:"metadata,omitempty"`
}

// NewTaskExecutor creates a new task executor
func NewTaskExecutor(llm llms.Model) (*TaskExecutor, error) {
	if llm == nil {
		return nil, fmt.Errorf("LLM model is required")
	}

	return &TaskExecutor{
		llm:          llm,
		logger:       slog.Default().With("component", "ai-executor"),
		runningTasks: make(map[string]*ExecutionContext),
	}, nil
}

// ExecutePlan executes a task plan
func (te *TaskExecutor) ExecutePlan(plan *TaskPlan) (*ExecutionResult, error) {
	te.logger.Info("Starting plan execution", "plan_id", plan.ID)

	ctx, cancel := context.WithTimeout(context.Background(), plan.Estimated*2) // Allow 2x estimated time
	defer cancel()

	// Create execution context
	execCtx := &ExecutionContext{
		TaskID:      plan.ID,
		Plan:        plan,
		CurrentStep: 0,
		Status:      "running",
		StartTime:   time.Now(),
		Results:     make(map[string]string),
		Errors:      make([]error, 0),
		Cancel:      cancel,
	}

	// Register running task
	te.mu.Lock()
	te.runningTasks[plan.ID] = execCtx
	te.mu.Unlock()

	// Execute plan
	result := te.executePlanSteps(ctx, execCtx)

	// Cleanup
	te.mu.Lock()
	delete(te.runningTasks, plan.ID)
	te.mu.Unlock()

	te.logger.Info("Plan execution completed", "plan_id", plan.ID, "status", result.Status)
	return result, nil
}

// executePlanSteps executes the steps of a plan
func (te *TaskExecutor) executePlanSteps(ctx context.Context, execCtx *ExecutionContext) *ExecutionResult {
	startTime := time.Now()
	
	for i, step := range execCtx.Plan.Steps {
		select {
		case <-ctx.Done():
			return te.createResult(execCtx, "cancelled", startTime, fmt.Errorf("execution cancelled"))
		default:
		}

		execCtx.CurrentStep = i
		te.logger.Debug("Executing step", "step_id", step.ID, "description", step.Description)

		// Check dependencies
		if err := te.checkStepDependencies(step, execCtx); err != nil {
			execCtx.Errors = append(execCtx.Errors, err)
			return te.createResult(execCtx, "failed", startTime, err)
		}

		// Execute step
		result, err := te.executeStep(ctx, step, execCtx)
		if err != nil {
			execCtx.Errors = append(execCtx.Errors, err)
			
			// Check if this is a critical step
			if te.isCriticalStep(step) {
				return te.createResult(execCtx, "failed", startTime, err)
			}
			
			// Continue with non-critical step failure
			te.logger.Warn("Non-critical step failed, continuing", "step_id", step.ID, "error", err)
			execCtx.Results[step.ID] = fmt.Sprintf("FAILED: %v", err)
		} else {
			execCtx.Results[step.ID] = result
		}
	}

	return te.createResult(execCtx, "completed", startTime, nil)
}

// executeStep executes a single step
func (te *TaskExecutor) executeStep(ctx context.Context, step TaskStep, execCtx *ExecutionContext) (string, error) {
	stepCtx, cancel := context.WithTimeout(ctx, 5*time.Minute) // Default step timeout
	defer cancel()

	// If no tool specified, use LLM to process the step
	if step.Tool == "" {
		return te.executeLLMStep(stepCtx, step, execCtx)
	}

	// Execute using specified tool
	return te.executeToolStep(stepCtx, step, execCtx)
}

// executeLLMStep executes a step using the LLM
func (te *TaskExecutor) executeLLMStep(ctx context.Context, step TaskStep, execCtx *ExecutionContext) (string, error) {
	prompt := fmt.Sprintf(`Execute the following task step:

Step: %s
Input: %s
Expected Output: %s

Previous Results: %v

Please provide a detailed response for this step.`, 
		step.Description, step.Input, step.Expected, execCtx.Results)

	response, err := llms.GenerateFromSinglePrompt(ctx, te.llm, prompt)
	if err != nil {
		return "", fmt.Errorf("LLM step execution failed: %w", err)
	}

	return response, nil
}

// executeToolStep executes a step using a specific tool
func (te *TaskExecutor) executeToolStep(ctx context.Context, step TaskStep, execCtx *ExecutionContext) (string, error) {
	// This would integrate with the actual tools
	// For now, return a placeholder
	return fmt.Sprintf("Tool '%s' executed with input: %s", step.Tool, step.Input), nil
}

// checkStepDependencies checks if step dependencies are satisfied
func (te *TaskExecutor) checkStepDependencies(step TaskStep, execCtx *ExecutionContext) error {
	for _, depID := range step.Dependencies {
		if _, exists := execCtx.Results[depID]; !exists {
			return fmt.Errorf("dependency %s not satisfied for step %s", depID, step.ID)
		}
	}
	return nil
}

// isCriticalStep determines if a step is critical for plan success
func (te *TaskExecutor) isCriticalStep(step TaskStep) bool {
	// Check metadata for criticality
	if critical, ok := step.Metadata["critical"].(bool); ok {
		return critical
	}
	
	// Default: steps with dependencies are considered critical
	return len(step.Dependencies) > 0
}

// createResult creates an execution result
func (te *TaskExecutor) createResult(execCtx *ExecutionContext, status string, startTime time.Time, finalError error) *ExecutionResult {
	errors := make([]string, len(execCtx.Errors))
	for i, err := range execCtx.Errors {
		errors[i] = err.Error()
	}
	
	if finalError != nil {
		errors = append(errors, finalError.Error())
	}

	return &ExecutionResult{
		TaskID:        execCtx.TaskID,
		Status:        status,
		Results:       execCtx.Results,
		Errors:        errors,
		Duration:      time.Since(startTime),
		StepsExecuted: execCtx.CurrentStep + 1,
		Metadata: map[string]interface{}{
			"total_steps": len(execCtx.Plan.Steps),
			"start_time":  startTime,
			"end_time":    time.Now(),
		},
	}
}

// GetRunningTasks returns information about currently running tasks
func (te *TaskExecutor) GetRunningTasks() map[string]*ExecutionContext {
	te.mu.RLock()
	defer te.mu.RUnlock()

	// Return a copy to prevent external modification
	running := make(map[string]*ExecutionContext)
	for id, ctx := range te.runningTasks {
		running[id] = ctx
	}

	return running
}

// CancelTask cancels a running task
func (te *TaskExecutor) CancelTask(taskID string) error {
	te.mu.RLock()
	execCtx, exists := te.runningTasks[taskID]
	te.mu.RUnlock()

	if !exists {
		return fmt.Errorf("task not found: %s", taskID)
	}

	execCtx.Cancel()
	te.logger.Info("Task cancelled", "task_id", taskID)
	return nil
}

// GetTaskStatus returns the status of a task
func (te *TaskExecutor) GetTaskStatus(taskID string) (string, error) {
	te.mu.RLock()
	defer te.mu.RUnlock()

	execCtx, exists := te.runningTasks[taskID]
	if !exists {
		return "not_found", fmt.Errorf("task not found: %s", taskID)
	}

	return execCtx.Status, nil
}

// ExecuteStepAsync executes a single step asynchronously
func (te *TaskExecutor) ExecuteStepAsync(step TaskStep, callback func(string, error)) {
	go func() {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
		defer cancel()

		// Create minimal execution context for single step
		execCtx := &ExecutionContext{
			TaskID:  fmt.Sprintf("step_%d", time.Now().Unix()),
			Results: make(map[string]string),
			Errors:  make([]error, 0),
		}

		result, err := te.executeStep(ctx, step, execCtx)
		callback(result, err)
	}()
}

// ValidateExecution validates if a plan can be executed
func (te *TaskExecutor) ValidateExecution(plan *TaskPlan) error {
	// Check if all required tools are available
	for _, step := range plan.Steps {
		if step.Tool != "" {
			if err := te.validateTool(step.Tool); err != nil {
				return fmt.Errorf("tool validation failed for step %s: %w", step.ID, err)
			}
		}
	}

	// Check resource requirements
	if err := te.checkResourceRequirements(plan); err != nil {
		return fmt.Errorf("resource check failed: %w", err)
	}

	return nil
}

// validateTool validates if a tool is available and functional
func (te *TaskExecutor) validateTool(toolName string) error {
	// This would check if the tool is available and functional
	// For now, assume all tools are available
	validTools := map[string]bool{
		"search": true, "code_analysis": true, "database_query": true,
		"kubernetes": true, "docker": true, "file": true, "task": true,
		"git": true, "cloudflare": true, "system": true,
	}

	if !validTools[toolName] {
		return fmt.Errorf("unknown tool: %s", toolName)
	}

	return nil
}

// checkResourceRequirements checks if system resources are sufficient
func (te *TaskExecutor) checkResourceRequirements(plan *TaskPlan) error {
	// This would check system resources like memory, disk space, etc.
	// For now, assume resources are sufficient
	return nil
}

// GetExecutionMetrics returns execution metrics
func (te *TaskExecutor) GetExecutionMetrics() map[string]interface{} {
	te.mu.RLock()
	defer te.mu.RUnlock()

	return map[string]interface{}{
		"running_tasks":    len(te.runningTasks),
		"total_executed":   0, // Would track total executed tasks
		"success_rate":     0.95, // Would calculate actual success rate
		"average_duration": "5m", // Would calculate actual average
	}
}

// TODO: Implement parallel step execution
// TODO: Implement step retry mechanisms
// TODO: Implement execution rollback capabilities
// TODO: Implement execution monitoring and alerting
// TODO: Implement execution result caching
// TODO: Implement execution performance optimization
