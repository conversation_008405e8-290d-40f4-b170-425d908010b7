// Package views provides the Docker module UI with Material Design 3 and cyberpunk styling
package views

import (
	"fmt"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"

	"assistant-go/internal/config"
	"assistant-go/pkg/cybertheme"
)

// DockerView represents the Docker module interface
type DockerView struct {
	config    *config.Config
	theme     *cybertheme.CyberTheme
	container *container.Scroll
	content   fyne.CanvasObject

	// Docker components
	containerList *widget.List
	imageList     *widget.List
	volumeList    *widget.List
	networkList   *widget.List
	logViewer     *widget.Entry
	statusLabel   *widget.Label
	dockerStatus  *widget.Label

	// Current state
	containers        []DockerContainer
	images            []DockerImage
	volumes           []DockerVolume
	networks          []DockerNetwork
	selectedContainer int
	selectedImage     int
	currentContainer  string
}

// DockerContainer represents a Docker container
type DockerContainer struct {
	ID      string
	Name    string
	Image   string
	Status  string
	Ports   string
	Created time.Time
}

// DockerImage represents a Docker image
type DockerImage struct {
	ID      string
	Name    string
	Tag     string
	Size    string
	Created time.Time
}

// DockerVolume represents a Docker volume
type DockerVolume struct {
	Name       string
	Driver     string
	Mountpoint string
	Created    time.Time
}

// DockerNetwork represents a Docker network
type DockerNetwork struct {
	ID     string
	Name   string
	Driver string
	Scope  string
}

// NewDockerView creates a new Docker module view
func NewDockerView(cfg *config.Config, theme *cybertheme.CyberTheme) (*DockerView, error) {
	view := &DockerView{
		config: cfg,
		theme:  theme,
		containers: []DockerContainer{
			{ID: "abc123", Name: "web-app", Image: "nginx:latest", Status: "Running", Ports: "80:8080", Created: time.Now().Add(-2 * time.Hour)},
			{ID: "def456", Name: "database", Image: "postgres:13", Status: "Running", Ports: "5432:5432", Created: time.Now().Add(-1 * time.Hour)},
			{ID: "ghi789", Name: "redis-cache", Image: "redis:alpine", Status: "Stopped", Ports: "6379:6379", Created: time.Now().Add(-30 * time.Minute)},
		},
		images: []DockerImage{
			{ID: "img001", Name: "nginx", Tag: "latest", Size: "133MB", Created: time.Now().Add(-24 * time.Hour)},
			{ID: "img002", Name: "postgres", Tag: "13", Size: "314MB", Created: time.Now().Add(-12 * time.Hour)},
			{ID: "img003", Name: "redis", Tag: "alpine", Size: "32MB", Created: time.Now().Add(-6 * time.Hour)},
		},
		volumes: []DockerVolume{
			{Name: "postgres_data", Driver: "local", Mountpoint: "/var/lib/docker/volumes/postgres_data", Created: time.Now().Add(-24 * time.Hour)},
			{Name: "app_logs", Driver: "local", Mountpoint: "/var/lib/docker/volumes/app_logs", Created: time.Now().Add(-12 * time.Hour)},
		},
		networks: []DockerNetwork{
			{ID: "net001", Name: "bridge", Driver: "bridge", Scope: "local"},
			{ID: "net002", Name: "app-network", Driver: "bridge", Scope: "local"},
		},
		selectedContainer: -1,
		selectedImage:     -1,
	}

	// Create main content
	content := view.createContent()

	// Create scrollable container with enhanced responsive design
	scrollContainer := container.NewScroll(content)
	scrollContainer.SetMinSize(fyne.NewSize(600, 400))
	scrollContainer.Direction = container.ScrollBoth

	view.container = scrollContainer
	view.content = content

	return view, nil
}

// createContent creates the main Docker interface
func (dv *DockerView) createContent() fyne.CanvasObject {
	// Create header
	header := widget.NewRichTextFromMarkdown(`
# 🐳 Docker Management

**Container Operations & Image Management**

---
`)

	// Create container management section
	containerSection := dv.createContainerSection()

	// Create image management section
	imageSection := dv.createImageSection()

	// Create volume management section
	volumeSection := dv.createVolumeSection()

	// Create network management section
	networkSection := dv.createNetworkSection()

	// Create status section
	statusSection := dv.createStatusSection()

	// Combine all sections with proper spacing
	return container.NewVBox(
		header,
		widget.NewSeparator(),
		containerSection,
		widget.NewSeparator(),
		imageSection,
		widget.NewSeparator(),
		volumeSection,
		widget.NewSeparator(),
		networkSection,
		widget.NewSeparator(),
		statusSection,
	)
}

// createContainerSection creates the container management interface
func (dv *DockerView) createContainerSection() fyne.CanvasObject {
	// Section header
	sectionHeader := widget.NewLabel("Container Management")
	sectionHeader.TextStyle.Bold = true

	// Container list
	dv.containerList = widget.NewList(
		func() int { return len(dv.containers) },
		func() fyne.CanvasObject {
			return container.NewHBox(
				widget.NewLabel("Container"),
				widget.NewLabel("Image"),
				widget.NewLabel("Status"),
				widget.NewLabel("Ports"),
			)
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			container := dv.containers[id]
			hbox := obj.(*fyne.Container)

			// Status icon
			statusIcon := "🟢"
			if container.Status != "Running" {
				statusIcon = "🔴"
			}

			// Update labels with safe ID slicing
			shortID := container.ID
			if len(container.ID) > 8 {
				shortID = container.ID[:8]
			}
			hbox.Objects[0].(*widget.Label).SetText(fmt.Sprintf("%s (%s)", container.Name, shortID))
			hbox.Objects[1].(*widget.Label).SetText(container.Image)
			hbox.Objects[2].(*widget.Label).SetText(fmt.Sprintf("%s %s", statusIcon, container.Status))
			hbox.Objects[3].(*widget.Label).SetText(container.Ports)
		},
	)
	dv.containerList.Resize(fyne.NewSize(800, 150))

	// Set up selection handling
	dv.containerList.OnSelected = func(id widget.ListItemID) {
		dv.selectedContainer = id
	}

	// Container action buttons with enhanced styling
	startBtn := cybertheme.CreateStandardButton("Start", func() {
		dv.startContainer()
	}, dv.theme)

	stopBtn := cybertheme.CreateStandardButton("Stop", func() {
		dv.stopContainer()
	}, dv.theme)

	restartBtn := cybertheme.CreateStandardButton("Restart", func() {
		dv.restartContainer()
	}, dv.theme)

	logsBtn := cybertheme.CreateStandardButton("Logs", func() {
		dv.viewContainerLogs()
	}, dv.theme)

	removeBtn := cybertheme.CreateStandardButton("Remove", func() {
		dv.removeContainer()
	}, dv.theme)

	containerButtons := container.NewHBox(startBtn, stopBtn, restartBtn, logsBtn, removeBtn)

	// Log viewer
	logLabel := widget.NewLabel("Container Logs:")
	logLabel.TextStyle.Bold = true

	dv.logViewer = widget.NewMultiLineEntry()
	dv.logViewer.SetText("2024-01-20 10:30:15 [INFO] Container started successfully\n2024-01-20 10:30:16 [INFO] Application listening on port 8080\n2024-01-20 10:30:17 [INFO] Health check passed")
	dv.logViewer.Resize(fyne.NewSize(800, 100))

	return container.NewVBox(
		sectionHeader,
		dv.containerList,
		containerButtons,
		logLabel,
		dv.logViewer,
	)
}

// createImageSection creates the image management interface
func (dv *DockerView) createImageSection() fyne.CanvasObject {
	// Section header
	sectionHeader := widget.NewLabel("Image Management")
	sectionHeader.TextStyle.Bold = true

	// Image list
	dv.imageList = widget.NewList(
		func() int { return len(dv.images) },
		func() fyne.CanvasObject {
			return container.NewHBox(
				widget.NewLabel("Image"),
				widget.NewLabel("Tag"),
				widget.NewLabel("Size"),
				widget.NewLabel("Created"),
			)
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			image := dv.images[id]
			hbox := obj.(*fyne.Container)

			// Update labels
			hbox.Objects[0].(*widget.Label).SetText(fmt.Sprintf("%s (%s)", image.Name, image.ID[:8]))
			hbox.Objects[1].(*widget.Label).SetText(image.Tag)
			hbox.Objects[2].(*widget.Label).SetText(image.Size)
			hbox.Objects[3].(*widget.Label).SetText(image.Created.Format("2006-01-02 15:04"))
		},
	)
	dv.imageList.Resize(fyne.NewSize(800, 120))

	// Set up selection handling
	dv.imageList.OnSelected = func(id widget.ListItemID) {
		dv.selectedImage = id
	}

	// Image action buttons with enhanced styling
	pullBtn := cybertheme.CreateStandardButton("Pull", func() {
		dv.pullImage()
	}, dv.theme)

	buildBtn := cybertheme.CreateStandardButton("Build", func() {
		dv.buildImage()
	}, dv.theme)

	pushBtn := cybertheme.CreateStandardButton("Push", func() {
		dv.pushImage()
	}, dv.theme)

	removeImgBtn := cybertheme.CreateStandardButton("Remove", func() {
		dv.removeImage()
	}, dv.theme)

	imageButtons := container.NewHBox(pullBtn, buildBtn, pushBtn, removeImgBtn)

	return container.NewVBox(
		sectionHeader,
		dv.imageList,
		imageButtons,
	)
}

// createVolumeSection creates the volume management interface
func (dv *DockerView) createVolumeSection() fyne.CanvasObject {
	// Section header
	sectionHeader := widget.NewLabel("Volume Management")
	sectionHeader.TextStyle.Bold = true

	// Volume list
	dv.volumeList = widget.NewList(
		func() int { return len(dv.volumes) },
		func() fyne.CanvasObject {
			return container.NewHBox(
				widget.NewLabel("Volume"),
				widget.NewLabel("Driver"),
				widget.NewLabel("Mountpoint"),
			)
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			volume := dv.volumes[id]
			hbox := obj.(*fyne.Container)

			// Update labels
			hbox.Objects[0].(*widget.Label).SetText(volume.Name)
			hbox.Objects[1].(*widget.Label).SetText(volume.Driver)
			hbox.Objects[2].(*widget.Label).SetText(volume.Mountpoint)
		},
	)
	dv.volumeList.Resize(fyne.NewSize(800, 100))

	// Volume action buttons
	createVolBtn := cybertheme.CreateStandardButton("Create", func() {
		dv.createVolume()
	}, dv.theme)

	removeVolBtn := cybertheme.CreateStandardButton("Remove", func() {
		dv.removeVolume()
	}, dv.theme)

	volumeButtons := container.NewHBox(createVolBtn, removeVolBtn)

	return container.NewVBox(
		sectionHeader,
		dv.volumeList,
		volumeButtons,
	)
}

// createNetworkSection creates the network management interface
func (dv *DockerView) createNetworkSection() fyne.CanvasObject {
	// Section header
	sectionHeader := widget.NewLabel("Network Management")
	sectionHeader.TextStyle.Bold = true

	// Network list
	dv.networkList = widget.NewList(
		func() int { return len(dv.networks) },
		func() fyne.CanvasObject {
			return container.NewHBox(
				widget.NewLabel("Network"),
				widget.NewLabel("Driver"),
				widget.NewLabel("Scope"),
			)
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			network := dv.networks[id]
			hbox := obj.(*fyne.Container)

			// Update labels
			hbox.Objects[0].(*widget.Label).SetText(fmt.Sprintf("%s (%s)", network.Name, network.ID[:8]))
			hbox.Objects[1].(*widget.Label).SetText(network.Driver)
			hbox.Objects[2].(*widget.Label).SetText(network.Scope)
		},
	)
	dv.networkList.Resize(fyne.NewSize(800, 100))

	// Network action buttons
	createNetBtn := cybertheme.CreateStandardButton("Create", func() {
		dv.createNetwork()
	}, dv.theme)

	removeNetBtn := cybertheme.CreateStandardButton("Remove", func() {
		dv.removeNetwork()
	}, dv.theme)

	networkButtons := container.NewHBox(createNetBtn, removeNetBtn)

	return container.NewVBox(
		sectionHeader,
		dv.networkList,
		networkButtons,
	)
}

// createStatusSection creates the status display
func (dv *DockerView) createStatusSection() fyne.CanvasObject {
	dv.statusLabel = widget.NewLabel(">>> DOCKER MODULE READY <<<")
	dv.statusLabel.Alignment = fyne.TextAlignCenter
	dv.statusLabel.TextStyle.Bold = true

	return dv.statusLabel
}

// Docker operation methods
func (dv *DockerView) startContainer() {
	if dv.selectedContainer >= 0 && dv.selectedContainer < len(dv.containers) {
		container := dv.containers[dv.selectedContainer]
		dv.currentContainer = container.Name
		dv.statusLabel.SetText(">>> STARTING CONTAINER <<<")
		dv.logViewer.SetText(fmt.Sprintf("Starting container: %s\n[INFO] Container started successfully", container.Name))
	}
}

func (dv *DockerView) stopContainer() {
	if dv.selectedContainer >= 0 && dv.selectedContainer < len(dv.containers) {
		container := dv.containers[dv.selectedContainer]
		dv.statusLabel.SetText(">>> STOPPING CONTAINER <<<")
		dv.logViewer.SetText(fmt.Sprintf("Stopping container: %s\n[INFO] Container stopped gracefully", container.Name))
	}
}

func (dv *DockerView) restartContainer() {
	dv.statusLabel.SetText(">>> RESTARTING CONTAINER <<<")
}

func (dv *DockerView) viewContainerLogs() {
	if dv.selectedContainer >= 0 && dv.selectedContainer < len(dv.containers) {
		container := dv.containers[dv.selectedContainer]
		dv.logViewer.SetText(fmt.Sprintf("Logs for container: %s\n\n2024-01-20 10:30:15 [INFO] Application started\n2024-01-20 10:30:16 [INFO] Processing requests...", container.Name))
		dv.statusLabel.SetText(">>> VIEWING CONTAINER LOGS <<<")
	}
}

func (dv *DockerView) removeContainer() {
	dv.statusLabel.SetText(">>> REMOVING CONTAINER <<<")
}

func (dv *DockerView) pullImage() {
	dv.statusLabel.SetText(">>> PULLING IMAGE <<<")
}

func (dv *DockerView) buildImage() {
	dv.statusLabel.SetText(">>> BUILDING IMAGE <<<")
}

func (dv *DockerView) pushImage() {
	dv.statusLabel.SetText(">>> PUSHING IMAGE <<<")
}

func (dv *DockerView) removeImage() {
	dv.statusLabel.SetText(">>> REMOVING IMAGE <<<")
}

func (dv *DockerView) createVolume() {
	dv.statusLabel.SetText(">>> CREATING VOLUME <<<")
}

func (dv *DockerView) removeVolume() {
	dv.statusLabel.SetText(">>> REMOVING VOLUME <<<")
}

func (dv *DockerView) createNetwork() {
	dv.statusLabel.SetText(">>> CREATING NETWORK <<<")
}

func (dv *DockerView) removeNetwork() {
	dv.statusLabel.SetText(">>> REMOVING NETWORK <<<")
}

// Content returns the Docker view's content
func (dv *DockerView) Content() fyne.CanvasObject {
	return dv.container
}

// Refresh refreshes the Docker view
func (dv *DockerView) Refresh() {
	if dv.container != nil {
		dv.container.Refresh()
	}
}
