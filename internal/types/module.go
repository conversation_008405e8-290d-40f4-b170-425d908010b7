// Package types provides shared types for Assistant-Go
// module.go - Module interface and health types
package types

import (
	"context"
	"time"

	"assistant-go/internal/config"
)

// Module represents a functional module in the application
// Following Go 1.24+ best practices with proper context handling
type Module interface {
	// Name returns the module name for identification
	Name() string

	// Initialize sets up the module with the given configuration
	Initialize(ctx context.Context, cfg *config.Config) error

	// Start begins the module's operation with context support
	Start(ctx context.Context) error

	// Stop gracefully shuts down the module with context support
	Stop(ctx context.Context) error

	// Health returns the current health status of the module
	Health() ModuleHealth
}

// ModuleHealth represents the health status of a module
type ModuleHealth struct {
	Status    string                 `json:"status"`              // healthy, degraded, unhealthy, pending
	Message   string                 `json:"message"`             // Human-readable status message
	LastCheck time.Time              `json:"last_check"`          // When the health was last checked
	Details   map[string]interface{} `json:"details,omitempty"`   // Additional health details
}
